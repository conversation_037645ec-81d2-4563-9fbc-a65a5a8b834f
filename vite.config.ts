import { defineConfig, ResolvedConfig } from 'vite';
import { createSvgIconsPlugin } from 'vite-plugin-svg-icons';
import vue from '@vitejs/plugin-vue';
import vueJsx from '@vitejs/plugin-vue-jsx';
import AutoImport from 'unplugin-auto-import/vite';
import Components from 'unplugin-vue-components/vite';
import { NaiveUiResolver, VantResolver } from 'unplugin-vue-components/resolvers';
import { viteCommonjs } from '@originjs/vite-plugin-commonjs';
import { htmlTransform } from './src/utils/vite/html-transform';
import { rem, remPreset } from 'vite-plugin-fz';
import baseConfig from './src/config/base';
import {
    AutoImportResolvers,
    AutoComponentsResolvers,
    AutoImportBusinessPreset
} from './src/utils/vite/auto-resolvers';
import legacy from '@vitejs/plugin-legacy';
import vueSetupExtend from 'vite-plugin-vue-setup-extend-plus';
import preprocessorPreset from 'wp-preprocessor/dist/preset';
import requestPreset from 'wp-request/dist/preset';
import AutoApi from 'vitejs-plugin-api-auto-import';
import UnoCSS from 'unocss/vite';
import HtmlSecurity from 'vite-plugin-html-security';
import path from 'path';
import md5 from 'md5';
import VueDevTools from 'vite-plugin-vue-devtools';
import GetVueRef from 'vitejs-get-vue-ref';
//@ts-ignore
import webAutoDeploy from 'web-auto-deploy/dist/vite';


// 构建时长统计插件
function buildTimePlugin() {
    let startTime: number;
    let buildStartTime: number;
    let transformStartTime: number;
    let bundleStartTime: number;

    const formatTime = (ms: number) => {
        if (ms < 1000) return `${ms}ms`;
        return `${(ms / 1000).toFixed(2)}s`;
    };

    return {
        name: 'build-time-plugin',
        configResolved(config: ResolvedConfig) {
            startTime = Date.now();
            const mode = config.command === 'build' ? '生产构建' : '开发模式';
            console.log(`\n⚡ Vite ${mode} 开始启动...`);
        },
        buildStart() {
            buildStartTime = Date.now();
            console.log('🚀 开始构建...');
        },
        transform() {
            if (!transformStartTime) {
                transformStartTime = Date.now();
                console.log('🔄 开始转换文件...');
            }
        },
        generateBundle() {
            bundleStartTime = Date.now();
            if (transformStartTime) {
                const transformTime = bundleStartTime - transformStartTime;
                console.log(`📝 文件转换完成，耗时: ${formatTime(transformTime)}`);
            }
            console.log('📦 开始生成 bundle...');
        },
        buildEnd() {
            const buildTime = Date.now() - buildStartTime;
            console.log(`✅ 构建完成，耗时: ${formatTime(buildTime)}`);
        },
        closeBundle() {
            const totalTime = Date.now() - startTime;
            const bundleTime = bundleStartTime ? Date.now() - bundleStartTime : 0;

            console.log(`🎉 Bundle 生成完成，耗时: ${formatTime(bundleTime)}`);
            console.log(`🏁 总耗时: ${formatTime(totalTime)}\n`);
        },
        // 开发服务器启动时间统计
        configureServer() {
            const devStartTime = Date.now();
            return () => {
                const devTime = Date.now() - devStartTime;
                console.log(`🔥 开发服务器启动完成，耗时: ${formatTime(devTime)}\n`);
            };
        }
    };
}

// https://vitejs.dev/config/
export default defineConfig({
    base: baseConfig.base,
    plugins: [
        buildTimePlugin(),
        UnoCSS(),
        vue(),
        createSvgIconsPlugin({
            iconDirs: [path.resolve(process.cwd(), 'src/icons')],
            symbolId: 'icon-[dir]-[name]',
            svgoOptions: {
                full: true
            }
        }),
        vueJsx(),
        viteCommonjs(),
        vueSetupExtend(),
        AutoImport({
            include: [
                /\.[tj]sx?$/, // .ts, .tsx, .js, .jsx
                /\.vue$/,
                /\.vue\?vue/, // .vue
                /\.md$/ // .md
            ],
            imports: [
                preprocessorPreset,
                requestPreset,
                remPreset,
                {
                    '@/api': ['api']
                },
                AutoImportBusinessPreset(),
                // presets
                'vue',
                'vue-router',
                '@vueuse/core',
                'pinia'
            ],
            eslintrc: {
                enabled: true
            },
            resolvers: [AutoImportResolvers()],
            dts: true,
            dirs: []
        }),
        Components({
            resolvers: [NaiveUiResolver(), VantResolver(), AutoComponentsResolvers()]
        }),
        htmlTransform(),
        rem(),
        legacy({
            targets: ['defaults', 'not IE 11'],
            /**
             * For chrome >= 61
             * global-this is vaild from chrome 70
             */
            modernPolyfills: ['es.global-this', 'es.array.flat']
        }),
        AutoApi({
            resolveAliasName: '@/api/apis',
            dir: 'src/api/apis'
        }),
        AutoApi({
            name: '$alert',
            resolveAliasName: '@/alert',
            dir: 'src/alert'
        }),
        AutoApi({
            name: '$utils',
            resolveAliasName: '@/utils/utils',
            dir: 'src/utils/utils'
        }),
        HtmlSecurity({ outputDir: 'dist_management' }),
        VueDevTools({
            launchEditor: 'code'
        }),
        AutoApi({
            name: '$hooks',
            resolveAliasName: '@/hooks',
            dir: 'src/hooks'
        }),
        AutoApi({
            name: '$datas',
            resolveAliasName: '@/data',
            dir: 'src/data'
        }),
        GetVueRef(),
        webAutoDeploy({
            interval: 1000 * 30
        } as any),

    ],
    resolve: {
        alias: {
            '@': path.resolve(__dirname, 'src')
        }
    },

    build: {
        target: 'es2015',
        chunkSizeWarningLimit: 5000,
        assetsInlineLimit: 0,
        outDir: 'dist_management',
        rollupOptions: {
            output: !baseConfig.filenameHash
                ? {}
                : {
                    chunkFileNames: (chunkInfo) => {
                        const newFileName = md5(
                            (chunkInfo.facadeModuleId || chunkInfo.name)
                                .replace(__dirname, '')
                                .replace(/\/|\\|\./g, '-')
                        );
                        return `assets/chunk-${newFileName.slice(0, 8)}.js`;
                    },
                    assetFileNames(chunkInfo) {
                        const newFileName = md5(
                            (chunkInfo.name || '').replace(__dirname, '').replace(/\/|\\|\./g, '-')
                        );
                        return `assets/${newFileName.slice(0, 8)}.[ext]`;
                    },
                    entryFileNames(chunkInfo) {
                        const newFileName = md5(
                            (chunkInfo.facadeModuleId || chunkInfo.name)
                                .replace(__dirname, '')
                                .replace(/\/|\\|\./g, '-')
                        );
                        return `assets/entry-${newFileName.slice(0, 8)}.js`;
                    }
                }
        }
    },
    server: {
        host: '0.0.0.0',
        hmr: {
            overlay: false
        }
    }
});
