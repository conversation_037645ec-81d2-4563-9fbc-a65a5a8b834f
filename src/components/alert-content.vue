<template>
    <div class="alert-content flex flex-col">
        <div class="alert-content-box">
            <slot></slot>
        </div>
        <div ref="footerRef" class="m-t-15px" :class="footerLayoutClass">
            <!-- 默认按钮区域 -->
            <n-space v-if="showDefaultButtons">
                <n-button
                    v-if="buttons.cancel?.show !== false"
                    :type="buttons.cancel?.type || 'default'"
                    @click="handleCancel"
                >
                    {{ buttons.cancel?.text || '取消' }}
                </n-button>
                <n-button v-if="buttons.save?.show !== false" type="primary" @click="handleSave">
                    {{ buttons.save?.text || '保存' }}
                </n-button>
                <!-- 额外按钮 -->
                <template v-for="(button, key) in buttons.extra" :key="key">
                    <n-button
                        v-if="button.show !== false"
                        :type="button.type || 'default'"
                        @click="handleExtraButton(key, button)"
                    >
                        {{ button.text }}
                    </n-button>
                </template>
            </n-space>
            <!-- 自定义 footer 插槽 -->
            <slot name="footer"></slot>
        </div>
    </div>
</template>

<script setup lang="ts">
interface ButtonConfig {
    text?: string;
    type?: 'default' | 'primary' | 'info' | 'success' | 'warning' | 'error';
    show?: boolean;
    onClick?: () => void | Promise<void>;
    emit?: string;
    autoClose?: boolean;
}

export interface ButtonsConfig {
    cancel?: ButtonConfig;
    save?: ButtonConfig;
    extra?: Record<string, ButtonConfig>;
}

const props = withDefaults(
    defineProps<{
        showDefaultButtons?: boolean;
        buttons?: ButtonsConfig;
        layout?: 'horizontal' | 'vertical';
        onDefaultCancel?: () => void | Promise<void>;
        onDefaultSave?: () => void | Promise<void>;
    }>(),
    {
        showDefaultButtons: true,
        buttons: () => ({}),
        layout: 'horizontal'
    }
);
const emit = defineEmits<{
    (e: 'cancel'): void;
    (e: 'save'): void;
    (e: string): void;
}>();
const footerRef = ref();
const { height } = useElementSize(footerRef);

// 布局样式计算
const footerLayoutClass = computed(() => {
    return props.layout === 'vertical' ? 'flex flex-col items-center gap-10px' : 'flex justify-center gap-10px';
});

const handleCancel = async () => {
    const cancelHandler = props.buttons.cancel?.onClick || props.onDefaultCancel;
    if (cancelHandler) {
        try {
            await cancelHandler();
        } catch (err) {
            return;
        }
    }
    emit('cancel');
    $alert.dialog.close();
};

const handleSave = async () => {
    const saveHandler = props.buttons.save?.onClick || props.onDefaultSave;
    if (saveHandler) {
        try {
            await saveHandler();
        } catch (err) {
            return;
        }
    }
    emit('save');
    $alert.dialog.close();
};

const handleExtraButton = async (key: string, button: ButtonConfig) => {
    if (button.onClick) {
        try {
            await button.onClick();
        } catch (err) {
            return;
        }
    }
    button.emit && emit(button.emit);
    button.autoClose && $alert.dialog.close();
};
</script>

<style scoped>
.alert-content {
    max-height: var(--alert-dialog-custom-theme-content-height);
    .alert-content-box {
        height: calc(var(--alert-dialog-custom-theme-content-height) - v-bind(height));
        overflow-x: hidden;
    }
}
</style>
