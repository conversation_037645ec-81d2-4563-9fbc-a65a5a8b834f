<template>
    <div class="notice-container" v-if="appConfig.actionBar.isShowNotice" @click="handleNotice">
        <n-icon size="18">
            <notifications-outline />
        </n-icon>
        <n-badge :value="noticeCount" :max="99" :offset="[0, -10]"> </n-badge>
    </div>
</template>

<script setup lang="ts">
import { NotificationsOutline } from '@vicons/ionicons5';
import useAppConfigStore from '@/store/modules/app-config';
import useStore from '@/store/modules/main';
import { type NotificationEvent } from '@/hooks/useSSE';
import { NButton, NEllipsis, NTag, NTime, type NotificationOptions } from 'naive-ui';

const store = useStore();
const appConfig = useAppConfigStore();

const noticeCount = ref();
const handleNotice = () => {
    $alert.dialog({
        title: '消息中心',
        width: '80%',
        content: import('@/components/business/notice-model.vue'),
        props: {
            onMarkRead: () => {
                getMsgTotal();
            }
        }
    });
};

// 创建弹窗
const createNotice = (data: NotificationOptions) => {
    window.$notification.create({
        content: data.content,
        meta: data.meta,
        action: data.action,
        closable: true
    });
};

// 更多
const handleMore = () => {
    window.$notification.destroyAll();
    window.$dialog.destroyAll();
    handleNotice();
};

// 建立sse连接，实时获取最新消息推送以及未读消息数量
let sseInstance: { close: () => void } | null = null;
const createSSE = () => {
    const sseUrl = `${import.meta.env.VITE_API || window.location.origin}/sse?Authorization=${store.token}`;
    sseInstance = $hooks.useSSE(
        sseUrl,
        (data: NotificationEvent) => handleData(data),
        (err: any) => {
            console.log(err);
        }
    );
};

const handleData = (data: NotificationEvent) => {
    // 站内消息通知
    if (data.kind === 1) {
        noticeCount.value = data.businessParam.unreadNotificationsCount;
        createNotice({
            content: () =>
                h('div', { class: 'flex gap-5px' }, [
                    h(
                        NTag,
                        {
                            type: levelMetaList.find((item) => item.value === data.businessParam.level)?.type,
                            size: 'small',
                            bordered: false
                        },
                        {
                            default: () => levelMetaList.find((item) => item.value === data.businessParam.level)?.label
                        }
                    ),
                    h(
                        NEllipsis,
                        {
                            lineClamp: 1,
                            tooltip: false
                        },
                        {
                            default: () => data.businessParam.content
                        }
                    )
                ]),
            meta: () =>
                h('div', { class: 'flex items-center justify-between' }, [
                    h(NTime, {
                        time: data.createdAt,
                        format: 'yyyy-MM-dd HH:mm',
                        class: 'c-#909090 text-12px'
                    }),
                    h('span', { class: 'c-#909090 text-12px ml-20px' }, `来源：${data.businessParam.moduleName} `)
                ]),
            action: () =>
                h(
                    NButton,
                    {
                        text: true,
                        type: 'info',
                        size: 'tiny',
                        onClick: () => handleMore()
                    },
                    { default: () => '更多' }
                )
        });
    } else if (data.kind === 2) {
        // 心跳包，不处理
    }
};

const getMsgTotal = async () => {
    const res = await $apis.yondu.api.v1.notification.list({ noPage: true, isRead: false });
    noticeCount.value = res.data.total;
};

const levelMetaList = [
    { value: 3, label: '紧急', type: 'error' },
    { value: 2, label: '预警', type: 'warning' },
    { value: 1, label: '普通', type: 'default' }
] as const;

onBeforeUnmount(() => {
    sseInstance?.close();
});

// 获取消息数据
onMounted(async () => {
    await getMsgTotal();
    createSSE();
});
</script>

<style scoped lang="less">
.notice-container {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 26px;
    height: 26px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
        background-color: rgba(255, 255, 255, 0.8);
    }
}

:deep(.n-badge) {
    .n-badge-sup {
        font-size: 10px;
        height: 12px;
        padding: 0 3px;
        .n-base-slot-machine {
            height: 12px;
            line-height: 12px;
        }
    }
}
</style>
