<template>
    <div class="paper-disposal-form">
        <n-form class="useFormDialog" label-placement="left" :show-feedback="false" :label-width="110">
            <n-grid :cols="24" :x-gap="10" :y-gap="10">
                <n-form-item-gi label="回收人" :span="12">
                    <n-input :value="modelValue?.disposer?.nickname || ''" readonly />
                </n-form-item-gi>
                <n-form-item-gi label="回收日期" :span="12">
                    <n-input :value="modelValue?.disposer?.disposeDate || ''" readonly placeholder="" />
                </n-form-item-gi>
                <n-form-item-gi label="发放类型" :span="12">
                    <n-input :value="modelValue?.distributeData?.distributeTypeText || ''" readonly placeholder="" />
                </n-form-item-gi>
                <n-form-item-gi label="文件类型" :span="12">
                    <n-input :value="modelValue?.distributeData?.fileTypeText || ''" readonly placeholder="" />
                </n-form-item-gi>
                <n-form-item-gi label="文件类别" :span="12">
                    <n-input :value="modelValue?.distributeData?.fileCategory || ''" readonly placeholder="" />
                </n-form-item-gi>
                <n-form-item-gi label="发放原因" :span="12">
                    <n-input :value="modelValue?.distributeData?.reason || ''" readonly placeholder="" />
                </n-form-item-gi>
                <n-form-item-gi label="其他原因" :span="24" v-if="modelValue?.distributeData?.otherReason">
                    <n-input :value="modelValue?.distributeData?.otherReason || ''" readonly placeholder="" />
                </n-form-item-gi>
                <n-form-item-gi
                    label="希望发放日期"
                    :span="12"
                    v-if="modelValue?.distributeData?.wishDistributeDateText"
                >
                    <n-input
                        :value="modelValue?.distributeData?.wishDistributeDateText || ''"
                        readonly
                        placeholder=""
                    />
                </n-form-item-gi>
                <n-form-item-gi label="处置方式" :span="12">
                    <n-input :value="modelValue?.disposalReason || ''" readonly placeholder="" />
                </n-form-item-gi>
            </n-grid>

            <div class="mt-16px">
                <n-form-item label="处置清单" class="data-table"> </n-form-item>
                <vxe-table :data="tableData" border>
                    <vxe-column field="fileName" title="文件名称" width="160" />
                    <vxe-column field="number" title="文件编号" width="140" />
                    <vxe-column field="version" title="版本/版次" width="120" />
                    <vxe-column field="fileForm" title="文件形式" width="100" />
                    <vxe-column field="distributeCount" title="发放份数" width="100" />
                    <vxe-column field="recycleCount" title="签收份数" width="100" />
                    <vxe-column field="disposalCount" title="处置份数" width="100" />
                    <vxe-column field="exchangePerson" title="交还人" width="200" />
                </vxe-table>
            </div>
        </n-form>
    </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { VxeTable, VxeColumn } from 'vxe-table';

const props = withDefaults(
    defineProps<{
        modelValue?: any;
    }>(),
    {
        modelValue: ''
    }
);
const { modelValue } = props;

// 表格数据
const tableData = computed(() => {
    const flowData = props.modelValue;
    const list = Array.isArray(flowData?.disposalList) ? flowData.disposalList : [];

    return list.map((item: any) => {
        return {
            fileName: item.permissions?.fileName || '',
            number: item.permissions?.number || '',
            version: item.permissions?.version || '',
            fileForm: item.permissions?.fileForm === 2 ? '纸质文件' : '电子文件',
            distributeCount: item.permissions?.distributeCount || 0,
            recycleCount: item.permissions?.recycleCount || 0,
            disposalCount: item.permissions?.disposalCount || 0,
            exchangePerson: item.permissions?.receivedByNames?.join('，') || ''
        };
    });
});
console.log(props.modelValue);
</script>

<style scoped lang="less">
.paper-disposal-form {
    width: 100%;
}
.data-table {
    :deep(.n-form-item-blank) {
        display: none;
    }
}
</style>
