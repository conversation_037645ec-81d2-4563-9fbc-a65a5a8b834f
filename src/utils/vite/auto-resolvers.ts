import { camelCase } from 'lodash';
export { AutoImportBusinessPreset } from '../scripts/build-pre';

// 获取业务组件的路径映射
const getBusinessComponentPath = (componentName: string): string | null => {
    try {
        const preset = require('../scripts/build-pre').AutoImportBusinessPreset();

        // 遍历预设配置，查找匹配的组件名
        for (const [importPath, aliases] of Object.entries(preset)) {
            if (Array.isArray(aliases)) {
                for (const [, aliasName] of aliases as [string, string][]) {
                    if (aliasName === componentName) {
                        return importPath;
                    }
                }
            }
        }
    } catch (error) {
        console.warn('Failed to load business preset:', error);
    }

    // 如果没有找到，回退到原来的逻辑
    const fileName = camelCase(componentName.replace(/^Bs/i, '').trim())
        .replace(/([A-Z])/g, '-$1')
        .toLowerCase();
    return `@/components/business/${fileName}.vue`;
};

const businessComponents = (name: string) => {
    const importPath = getBusinessComponentPath(name);
    if (!importPath) {
        return undefined;
    }
    return {
        as: name,
        name: 'default',
        from: importPath
    };
};
export const AutoImportResolvers = () => [
    (name: string) => {
        if (/^BS\w.*/i.test(name)) {
            return businessComponents(name);
        }
        const m = name.match(/^(use.*)Hooks$/);
        if (m) {
            return {
                as: name,
                name: m[1],
                from: '@/hooks'
            };
        }
    }
];
export const AutoComponentsResolvers = () => [
    (name: string) => {
        if (/^Bs\w.*/i.test(name)) {
            return businessComponents(name);
        }
    }
];
