import { createDiscreteApi } from 'naive-ui';

interface LoadingOptions {
    /** 显示时间（毫秒），默认为0表示不自动关闭 */
    duration?: number;
    /** 自定义显示文本，默认为"加载中..." */
    text?: string;
    /** 是否显示蒙版，默认为true */
    mask?: boolean;
}

interface LoadingInstance {
    /** 关闭loading */
    close: () => void;
    /** 等待loading完成的Promise（仅在设置了duration时有效） */
    promise?: Promise<void>;
}

class GlobalLoading {
    private loadingApi: ReturnType<typeof createDiscreteApi>['loadingBar'] | null = null;
    private currentTimer: NodeJS.Timeout | null = null;
    private isLoading = false;

    constructor() {
        // 初始化Naive UI的loading API
        const { loadingBar } = createDiscreteApi(['loadingBar']);
        this.loadingApi = loadingBar;
    }

    /**
     * 显示全局loading
     * @param options 配置选项
     * @returns LoadingInstance 包含关闭方法的实例
     */
    show(options: LoadingOptions = {}): LoadingInstance {
        const { duration = 0, text = '加载中...', mask = true } = options;

        // 如果已经有loading在显示，先关闭
        if (this.isLoading) {
            this.hide();
        }

        this.isLoading = true;

        // 创建蒙版元素
        let maskElement: HTMLElement | null = null;
        if (mask) {
            maskElement = this.createMaskElement(text);
            document.body.appendChild(maskElement);
        }

        // 启动Naive UI的loading bar
        this.loadingApi?.start();

        // 创建Promise用于等待loading完成
        let loadingPromise: Promise<void> | undefined;

        // 如果设置了持续时间，自动关闭
        if (duration > 0) {
            loadingPromise = new Promise<void>((resolve) => {
                this.currentTimer = setTimeout(() => {
                    this.hide();
                    resolve();
                }, duration);
            });
        }

        // 返回包含关闭方法的实例
        return {
            close: () => this.hide(),
            promise: loadingPromise
        };
    }

    /**
     * 隐藏全局loading
     */
    hide(): void {
        if (!this.isLoading) return;

        this.isLoading = false;

        // 清除定时器
        if (this.currentTimer) {
            clearTimeout(this.currentTimer);
            this.currentTimer = null;
        }

        // 完成loading bar
        this.loadingApi?.finish();

        // 移除蒙版元素
        const maskElement = document.querySelector('.global-loading-mask');
        if (maskElement) {
            maskElement.remove();
        }
    }

    /**
     * 创建蒙版元素
     * @param text 显示文本
     * @returns HTMLElement
     */
    private createMaskElement(text: string): HTMLElement {
        const maskElement = document.createElement('div');
        maskElement.className = 'global-loading-mask';
        maskElement.innerHTML = `
            <div class="global-loading-content">
                <div class="global-loading-spinner">
                    <div class="spinner-dot"></div>
                    <div class="spinner-dot"></div>
                    <div class="spinner-dot"></div>
                    <div class="spinner-dot"></div>
                    <div class="spinner-dot"></div>
                </div>
                <div class="global-loading-text">${text}</div>
            </div>
        `;

        // 添加样式
        const style = document.createElement('style');
        style.textContent = `
            .global-loading-mask {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background-color: rgba(0, 0, 0, 0.2);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 9999;
                backdrop-filter: blur(2px);
            }

            .global-loading-content {
                display: flex;
                flex-direction: column;
                align-items: center;
                background: white;
                padding: 30px;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                min-width: 200px;
            }

            .global-loading-spinner {
                display: flex;
                gap: 4px;
                margin-bottom: 16px;
            }

            .spinner-dot {
                width: 8px;
                height: 8px;
                border-radius: 50%;
                background-color: #18a058;
                animation: loading-bounce 1.4s ease-in-out infinite both;
            }

            .spinner-dot:nth-child(1) { animation-delay: -0.32s; }
            .spinner-dot:nth-child(2) { animation-delay: -0.16s; }
            .spinner-dot:nth-child(3) { animation-delay: 0s; }
            .spinner-dot:nth-child(4) { animation-delay: 0.16s; }
            .spinner-dot:nth-child(5) { animation-delay: 0.32s; }

            @keyframes loading-bounce {
                0%, 80%, 100% {
                    transform: scale(0);
                }
                40% {
                    transform: scale(1);
                }
            }

            .global-loading-text {
                color: #333;
                font-size: 14px;
                font-weight: 500;
            }

            /* 暗色主题适配 */
            [data-theme="dark"] .global-loading-content {
                background: #2d2d30;
                color: #fff;
            }

            [data-theme="dark"] .global-loading-text {
                color: #fff;
            }
        `;

        // 如果样式还没有添加到页面中，则添加
        if (!document.querySelector('#global-loading-styles')) {
            style.id = 'global-loading-styles';
            document.head.appendChild(style);
        }

        return maskElement;
    }

    /**
     * 检查是否正在loading
     */
    get loading(): boolean {
        return this.isLoading;
    }
}

// 创建全局实例
const globalLoading = new GlobalLoading();

/**
 * 显示全局loading
 * @param options 配置选项
 * @returns LoadingInstance 包含关闭方法的实例
 * 
 * @example
 * // 基础用法
 * const loading = showGlobalLoading();
 * // 手动关闭
 * loading.close();
 * 
 * @example
 * // 自动关闭
 * showGlobalLoading({ duration: 3000, text: '提交中...' });
 * 
 * @example
 * // 自定义文本
 * showGlobalLoading({ text: '数据处理中，请稍候...' });
 */
export const showGlobalLoading = (options?: LoadingOptions): LoadingInstance => {
    return globalLoading.show(options);
};

/**
 * 隐藏全局loading
 */
export const hideGlobalLoading = (): void => {
    globalLoading.hide();
};

/**
 * 检查是否正在loading
 */
export const isGlobalLoading = (): boolean => {
    return globalLoading.loading;
};

export default {
    show: showGlobalLoading,
    hide: hideGlobalLoading,
    loading: isGlobalLoading
};
