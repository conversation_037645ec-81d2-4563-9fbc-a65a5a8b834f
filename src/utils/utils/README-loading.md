# 全局Loading工具使用说明

## 概述

这个全局Loading工具提供了一个统一的、可配置的加载状态指示器，支持全屏蒙版、自定义文本、自动关闭等功能。

## 功能特性

- ✅ 全屏蒙版效果
- ✅ 自定义显示文本
- ✅ 可配置显示时间
- ✅ 手动控制开关
- ✅ 防止闪烁（最小显示时间）
- ✅ 防止卡死（最大显示时间）
- ✅ 暗色主题适配
- ✅ TypeScript支持
- ✅ 全局访问

## 基础用法

### 1. 导入方式

```typescript
// 方式1：直接导入
import { showGlobalLoading, hideGlobalLoading } from '@/utils/loading';

// 方式2：使用全局方法（推荐）
$utils.loading.show();
$utils.loading.hide();
```

### 2. 基础示例

```typescript
// 显示loading
const loading = showGlobalLoading();

// 手动关闭
loading.close();

// 或者使用全局方法关闭
hideGlobalLoading();
```

### 3. 自动关闭

```typescript
// 3秒后自动关闭
showGlobalLoading({
    duration: 3000,
    text: '提交中...'
});
```

### 4. 自定义文本

```typescript
showGlobalLoading({
    text: '数据处理中，请稍候...'
});
```

## 高级用法

### 1. 在API调用中使用

```typescript
const submitForm = async () => {
    const loading = showGlobalLoading({
        text: '正在提交表单...'
    });

    try {
        await api.submitForm(formData);
        window.$message.success('提交成功！');
    } catch (error) {
        window.$message.error('提交失败！');
    } finally {
        loading.close();
    }
};
```

### 2. 在请求拦截器中使用

```typescript
import { requestLoadingInterceptor } from '@/utils/request-loading';

// 配置请求拦截器
axios.interceptors.request.use(
    requestLoadingInterceptor.onRequest,
    requestLoadingInterceptor.onError
);

axios.interceptors.response.use(
    requestLoadingInterceptor.onResponse,
    requestLoadingInterceptor.onError
);

// 在API调用中指定loading配置
const response = await request({
    url: '/api/data',
    method: 'GET',
    loading: {
        enabled: true,
        text: '正在加载数据...',
        minDuration: 300,
        maxDuration: 30000
    }
});
```

## API参考

### showGlobalLoading(options?)

显示全局loading。

#### 参数

| 参数     | 类型    | 默认值      | 说明                              |
| -------- | ------- | ----------- | --------------------------------- |
| duration | number  | 0           | 显示时间（毫秒），0表示不自动关闭 |
| text     | string  | '加载中...' | 自定义显示文本                    |
| mask     | boolean | true        | 是否显示蒙版                      |

#### 返回值

返回一个包含 `close()` 方法的对象，用于手动关闭loading。

### hideGlobalLoading()

隐藏全局loading。

### isGlobalLoading()

检查是否正在loading。

#### 返回值

返回 `boolean` 值，表示当前是否正在显示loading。

## 全局方法

在全局注册后，可以通过 `$utils.loading` 访问：

```typescript
// 显示loading
$utils.loading.show({
    text: '加载中...',
    duration: 3000
});

// 隐藏loading
$utils.loading.hide();

// 检查loading状态
const isLoading = $utils.loading.loading();
```

## 样式定制

Loading组件支持暗色主题自动适配，也可以通过CSS变量进行定制：

```css
/* 自定义样式 */
.global-loading-mask {
    --loading-bg-color: rgba(0, 0, 0, 0.7);
    --loading-content-bg: #ffffff;
    --loading-text-color: #333333;
    --loading-spinner-color: #18a058;
}

/* 暗色主题 */
[data-theme="dark"] .global-loading-mask {
    --loading-content-bg: #2d2d30;
    --loading-text-color: #ffffff;
}
```

## 最佳实践

### 1. 在组件中使用

```vue
<template>
    <n-button @click="handleSubmit" :loading="submitting">
        提交
    </n-button>
</template>

<script setup>
const submitting = ref(false);

const handleSubmit = async () => {
    submitting.value = true;
    const loading = showGlobalLoading({
        text: '正在提交数据...'
    });

    try {
        await submitData();
        window.$message.success('提交成功！');
    } catch (error) {
        window.$message.error('提交失败！');
    } finally {
        submitting.value = false;
        loading.close();
    }
};
</script>
```

### 2. 批量操作

```typescript
const batchOperation = async (items: any[]) => {
    const loading = showGlobalLoading({
        text: `正在处理 ${items.length} 项数据...`
    });

    try {
        const results = await Promise.allSettled(
            items.map(item => processItem(item))
        );
        
        const successCount = results.filter(r => r.status === 'fulfilled').length;
        window.$message.success(`成功处理 ${successCount} 项数据`);
    } finally {
        loading.close();
    }
};
```

### 3. 长时间操作

```typescript
const longRunningTask = async () => {
    const loading = showGlobalLoading({
        text: '正在执行复杂计算，请耐心等待...',
        duration: 0 // 不自动关闭
    });

    try {
        // 长时间运行的任务
        await complexCalculation();
    } finally {
        loading.close();
    }
};
```

## 注意事项

1. **避免嵌套使用**：同时显示多个loading可能会造成用户困惑
2. **及时关闭**：确保在所有情况下都能正确关闭loading
3. **合理的文本**：使用描述性的文本告知用户当前操作
4. **错误处理**：在catch块中也要记得关闭loading
5. **性能考虑**：避免在短时间内频繁开关loading

## 故障排除

### Q: Loading不显示？
A: 检查是否正确导入了工具函数，确保Naive UI已正确配置。

### Q: Loading无法关闭？
A: 确保在finally块中调用了close()方法，或者检查是否有JavaScript错误。

### Q: 样式不正确？
A: 检查CSS是否被其他样式覆盖，确保z-index足够高。

### Q: 在移动端显示异常？
A: 检查viewport设置，确保backdrop-filter属性的兼容性。
