// 转换树形数据
const convertTreeData = (data: any[]): any[] => {
    return data.map((item) => ({
        ...item,
        label: item.name || item.label || item.documentName || '',
        value: item.id || item.value || item.documentId || '',
        key: item.id || item.value || item.documentId || new Date().getTime().toString(),
        children: Array.isArray(item.children) && item.children.length > 0 ? convertTreeData(item.children) : undefined
    }));
};

export default {
    convertTreeData
};
