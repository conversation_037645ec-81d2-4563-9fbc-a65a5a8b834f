export default {
    allowBorrow(params: AllowBorrowRequest) {
        return request({
            url: '/nebula/api/v1/document-library/allow/borrows',
            method: 'get',
            params
        });
    }
}

export interface AllowBorrowRequest {
    /**
     * 文档分类 id
     */
    documentCategoryId?: string;
    /**
     * 文档名称
     */
    documentName?: string;
    /**
     * 文档编号
     */
    documentNo?: string;
    /**
     * 2 内部库 3 外部库
     */
    documentKind?: number;
    /**
     *  1- 即将作废：文件库中搜索 2即将实施：文件库中搜索 3- 有效：文件库中搜索 4 拟修订：文件库中搜索 -1 作废：作废文件库中搜索
     */
    validityKind?: number;
    [property: string]: any;
}
