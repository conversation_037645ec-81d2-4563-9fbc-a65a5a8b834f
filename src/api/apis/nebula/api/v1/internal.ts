export default {
    list(params: InternalDocumentListRequest) {
        return request({
            url: '/nebula/api/v1/internal/documents',
            method: 'post',
            data: {
                ...params,
                hasAttachment: params.hasAttachment ?? 0
            }
        });
    },
    get(id: string) {
        return request({
            url: '/nebula/api/v1/internal/document',
            method: 'get',
            params: {
                id
            }
        });
    },
    create(data: InternalDocumentCreateRequest) {
        return request({
            url: '/nebula/api/v1/internal/document/create',
            method: 'post',
            data
        });
    },
    change(data: InternalDocumentChangeRequest) {
        return request({
            url: '/nebula/api/v1/internal/document/change',
            method: 'post',
            data
        });
    },
    import(data: ImportRequest) {
        return request({
            url: '/nebula/api/v1/document-library/import',
            method: 'post',
            data
        });
    },
    export(data: ExportRequest) {
        return request({
            url: '/nebula/api/v1/document-library/export',
            method: 'post',
            data
        });
    }
};

export interface InternalDocumentListRequest {
    page?: number;
    pageSize?: number;
    noPage?: boolean;
    /**
     * 部门ids
     */
    departmentIds?: string[];
    /**
     * 类别ids
     */
    docCategoryIds?: string[];
    /**
     * 搜索关键字
     */
    search?: string;
    /**
     * 状态 1- 即将作废 2- 即将实施 3- 有效 4- 拟修订
     */
    status?: number;
    /**
     * 是否有附件
     */
    hasAttachment?: number;
    [property: string]: any;
}

export interface InternalDocument {
    /**
     * 审批信息
     */
    approvalInfo?: ApprovalInfo;
    /**
     * 编制人ID
     */
    authorId?: string;
    /**
     * 编制部门ID
     */
    departmentId?: string;
    /**
     * 文件类别 用“-”拼接
     */
    docCategoryNames?: string;
    /**
     * 实施日期
     */
    effectiveDate?: number;
    /**
     * 文档ID
     */
    id?: string;
    /**
     * 文件名称
     */
    name?: string;
    /**
     * 文档编号
     */
    no?: string;
    /**
     * 原文件编号
     */
    originalNo?: string;
    /**
     * 原文件版本
     */
    originalVersionNo?: string;
    /**
     * 发布日期
     */
    publishDate?: number;
    /**
     * 1- 即将作废 2- 即将实施 3- 有效 4- 拟修订
     */
    status?: number;
    /**
     * 版本号
     */
    versionNo?: string;
    [property: string]: any;
}

/**
 * 审批信息
 */
export interface ApprovalInfo {
    /**
     * 批准人列表
     */
    approvers?: Approver[];
    /**
     * 审核人列表
     */
    auditors?: Auditor[];
    [property: string]: any;
}

export interface Approver {
    /**
     * 通过日期
     */
    passedDate?: number;
    /**
     * 用户ID
     */
    userId?: string;
    userNickname: string;
    [property: string]: any;
}

export interface Auditor {
    /**
     * 通过日期
     */
    passedDate?: number;
    /**
     * 用户ID
     */
    userId?: string;
    userNickname: string;
    [property: string]: any;
}

export interface InternalDocumentCreateRequest {
    /**
     * 编制人
     */
    authorId?: string | null;
    /**
     * 编制部门
     */
    departmentId?: string | null;
    /**
     * 文件类别
     */
    docCategoryId?: string | null;
    /**
     * 实施日期
     */
    effectiveDate?: number | null;
    /**
     * 文件id
     */
    fileId?: string | null;
    /**
     * 文件名称
     */
    name?: string;
    /**
     * 原文件编号
     */
    originalNo?: string;
    /**
     * 原文件版本
     */
    originalVersionNo?: string;
    /**
     * 发布日期
     */
    publishDate?: number | null;
    [property: string]: any;
}

export interface InternalDocumentChangeRequest extends InternalDocumentCreateRequest {
    id?: string | null;
}

export interface ImportRequest {
    /**
     * 清单文件列表
     */
    listFileIds: string[];
    /**
     * 台账文件 id
     */
    mainFileId: string;
    /**
     * 模块类型，1-书籍库 2-内部库 3-外部库（集团库） 4-外部库（公司库）
     */
    moduleType: number;
    [property: string]: any;
    // 类型字典id
    typeDictionaryId: string;
    // 领域字典id
    domainDictionaryId?: string | null;
    //认证字典id
    authDictionaryId?: string | null;
}

export interface ExportRequest {
    /**
     * 模块类型，1-书籍库 2-内部库 3-外部库
     */
    moduleType: number;
    /**
     * 列表请求参数
     */
    params: { [key: string]: any };
    [property: string]: any;
}
