<template>
    <alert-content :on-default-save="submit">
        <n-descriptions
            label-placement="left"
            label-align="right"
            label-style="width: 90px;"
            bordered
            :column="2"
            size="small"
        >
            <n-descriptions-item label="文件名称" :span="2"> {{ data.documentName }} </n-descriptions-item>
            <n-descriptions-item label="文件有效性"> {{ data.documentValidity }} </n-descriptions-item>
            <n-descriptions-item label="文件类型"> {{ data.documentModuleType }} </n-descriptions-item>
            <n-descriptions-item label="文件类别"> {{ data.documentCategoryName }} </n-descriptions-item>
            <n-descriptions-item label="文件编号"> {{ data.documentNo }} </n-descriptions-item>
            <n-descriptions-item label="版本/版次"> {{ data.documentVersionNo }} </n-descriptions-item>
        </n-descriptions>
        <n-form
            ref="formRef"
            class="mt-15px"
            :model="form"
            :rules="rules"
            label-placement="left"
            require-mark-placement="left"
            :label-width="110"
            :show-feedback="false"
        >
            <n-grid :cols="24" :y-gap="10" :x-gap="10">
                <n-form-item-gi label="借阅日期" path="borrowPeriod" :span="24">
                    <n-date-picker
                        v-model:value="form.borrowPeriod"
                        type="daterange"
                        format="yyyy-MM-dd"
                        placeholder="开始日期-结束日期"
                        class="w-full"
                    />
                </n-form-item-gi>

                <n-form-item-gi :span="12" label="借阅原因" path="reason">
                    <n-select
                        v-model:value="form.reason"
                        :options="$datas.borrowing.reasonOptions"
                        placeholder="请选择发放原因"
                        clearable
                    />
                </n-form-item-gi>
                <n-form-item-gi v-if="form.reason === 5" :span="24" label="其他原因" path="otherReason">
                    <n-input
                        v-model:value="form.otherReason"
                        maxlength="50"
                        placeholder="请输入其他借阅原因"
                        show-count
                    />
                </n-form-item-gi>
            </n-grid>
        </n-form>
    </alert-content>
</template>

<script setup lang="ts">
import useStore from '@/store/modules/main';
import dayjs from 'dayjs';
import { FormRules } from 'naive-ui';

const store = useStore();

const props = defineProps<{
    row: any;
    type: number; // 2-内部文件 3-外部文件
}>();
console.log(props.row, 'props.row');

const formRef = ref();
const data = computed(() => {
    return {
        documentName: props.row.name,
        documentValidity: props.row.documentValidity,
        documentModuleType: props.type,
        documentCategoryName: props.row.docCategoryName,
        documentNo: props.row.no,
        documentVersionNo: props.row.versionNo
    };
});
const rules: FormRules = {
    borrowPeriod: { required: true, message: '请输入借阅日期', trigger: ['blur', 'change'], type: 'array' },
    reason: { required: true, message: '请选择借阅原因', trigger: ['blur', 'change'], type: 'number' },
    otherReason: { required: true, message: '请输入其他借阅原因', trigger: ['blur', 'change'] }
};

const form = ref({
    borrowPeriod: null as [number, number] | null,
    reason: null as number | null,
    otherReason: null as string | null
});

const submit = async () => {
    try {
        await formRef.value?.validate();
    } catch (err: any) {
        window.$message.error(err[0][0].message);
        return Promise.reject();
    }

    // 组装借阅业务表单数据
    const _data: any = {
        reason: form.value.reason,
        otherReason: form.value.reason === 5 ? form.value.otherReason : '',
        borrowTime: form.value.borrowPeriod?.[0],
        dueTime: form.value.borrowPeriod?.[1],
        documents: [
            {
                documentId: props.row.id,
                documentModuleType: props.type,
                documentVersionNo: props.row.versionNo
            }
        ]
    };
    let borrowRecordId;
    await $apis.nebula.api.v1.documentLibrary.loans.create(_data).then((res) => {
        borrowRecordId = res.data.id;
    });

    // 组装借阅审批数据
    const documents = [
        {
            documentValidity: props.row.status,
            documentModuleType: props.type,
            documentCategoryId: props.row.docCategoryName,
            documentId: props.row.name,
            documentNo: props.row.no,
            documentVersionNo: props.row.versionNo
        }
    ];
    const result = {
        userNickname: store.userInfo.nickname,
        approvalApplyTime: dayjs().format('YYYY-MM-DD'),
        borrowPeriod: form.value.borrowPeriod,
        reason: form.value.reason,
        otherReason: form.value.reason === 5 ? form.value.otherReason : '',
        documents
    };
    const formData = JSON.stringify({
        businessId: 'FILE_BORROW',
        version: '1.0.0',
        borrowRecordId,
        data: result
    });
    await $hooks.useApprovalProcess('FILE_BORROW', formData);
    window.$message.success('借阅申请已提交');
};
</script>

<style scoped></style>
