<template>
    <div class="recycle-list-vxe-table">
        <n-data-table
            :columns="columns"
            :data="tableData"
            :row-key="(row) => row.fileId"
            :checked-row-keys="checkedRowKeys"
            @update:checked-row-keys="handleSelectionChange"
            :scroll-x="1600"
            :loading="loading"
        >
            <template #eFileLook="{ row }">
                <n-scrollbar class="w-100% max-h-80px">
                    <n-checkbox-group
                        :value="getCheckedUsers(row.fileId, 1)"
                        @update:value="(val) => handlePermissionChange(row, 'eFileLook', val)"
                    >
                        <n-grid :cols="12" :y-gap="5" :x-gap="5">
                            <n-gi v-for="item in row.eFileLook?.receivedBy || []" :key="item.userId" :span="4">
                                <n-checkbox
                                    :value="item.userId"
                                    :label="item.nickname"
                                    size="small"
                                    :disabled="item.status !== 1"
                                />
                            </n-gi>
                        </n-grid>
                    </n-checkbox-group>
                </n-scrollbar>
            </template>
            <template #eFileLookAndDownload="{ row }">
                <n-scrollbar class="w-100% max-h-80px">
                    <n-checkbox-group
                        :value="getCheckedUsers(row.fileId, 2)"
                        @update:value="(val) => handlePermissionChange(row, 'eFileLookAndDownload', val)"
                    >
                        <n-grid :cols="12" :y-gap="5" :x-gap="5">
                            <n-gi
                                v-for="item in row.eFileLookAndDownload?.receivedBy || []"
                                :key="item.userId"
                                :span="4"
                            >
                                <n-checkbox
                                    :value="item.userId"
                                    :label="item.nickname"
                                    size="small"
                                    :disabled="item.status !== 1"
                                />
                            </n-gi>
                        </n-grid>
                    </n-checkbox-group>
                </n-scrollbar>
            </template>
            <template #paperDocumentOnceDownload="{ row }">
                <n-scrollbar class="w-100% max-h-80px">
                    <n-checkbox-group
                        :value="getCheckedUsers(row.fileId, 3)"
                        @update:value="(val) => handlePermissionChange(row, 'paperDocumentOnceDownload', val)"
                    >
                        <n-grid :cols="12" :y-gap="5" :x-gap="5">
                            <n-gi
                                v-for="item in row.paperDocumentOnceDownload?.receivedBy || []"
                                :key="item.userId"
                                :span="4"
                            >
                                <n-checkbox
                                    :value="item.userId"
                                    :label="item.nickname"
                                    size="small"
                                    :disabled="item.status !== 1"
                                />
                            </n-gi>
                        </n-grid>
                    </n-checkbox-group>
                </n-scrollbar>
            </template>
            <template #eFileOnceDownload="{ row }">
                <n-scrollbar class="w-100% max-h-80px">
                    <n-checkbox-group
                        :value="getCheckedUsers(row.fileId, 4)"
                        @update:value="(val) => handlePermissionChange(row, 'eFileOnceDownload', val)"
                    >
                        <n-grid :cols="12" :y-gap="5" :x-gap="5">
                            <n-gi v-for="item in row.eFileOnceDownload?.receivedBy || []" :key="item.userId" :span="4">
                                <n-checkbox
                                    :value="item.userId"
                                    :label="item.nickname"
                                    size="small"
                                    :disabled="item.status !==1"
                                />
                            </n-gi>
                        </n-grid>
                    </n-checkbox-group>
                </n-scrollbar>
            </template>
            <template #todo="{ row }">
                <n-button @click="showRecycleDialog(row)" size="tiny" type="primary">回收记录</n-button>
            </template>
        </n-data-table>
    </div>
</template>

<script setup lang="ts">
interface TableRow {
    id: string;
    fileId: string;
    fileName: string;
    number: string;
    version: string;
    eFileLook?: {
        fileForm: number;
        filePermission: number;
        receivedBy: Array<{
            userId: string;
            nickname: string;
            status: number;
        }>;
    };
    eFileLookAndDownload?: {
        fileForm: number;
        filePermission: number;
        receivedBy: Array<{
            userId: string;
            nickname: string;
            status: number;
        }>;
    };
    paperDocumentOnceDownload?: {
        fileForm: number;
        filePermission: number;
        receivedBy: Array<{
            userId: string;
            nickname: string;
            status: number;
        }>;
    };
    eFileOnceDownload?: {
        fileForm: number;
        filePermission: number;
        receivedBy: Array<{
            userId: string;
            nickname: string;
            status: number;
        }>;
    };
}

interface BackDataItem {
    inventoryId: string;
    permissions: Array<{
        filePermission: number;
        receivedBy: string[];
    }>;
}

const props = defineProps<{ modelValue: BackDataItem[]; id: string }>();
const emit = defineEmits(['update:modelValue']);

const columns = ref<any[]>([
    { type: 'selection', width: 50, fixed: 'left' },
    { title: '序号', key: 'key', align: 'center', width: 60, render: (_: any, index: number) => `${index + 1}` },
    { key: 'fileName', title: '文件名称', align: 'center', fixed: 'left', width: 100, ellipsis: { tooltip: true } },
    { key: 'number', title: '文件编号', align: 'center', width: 100, ellipsis: { tooltip: true } },
    { key: 'version', title: '版本/版次', align: 'center', width: 100, ellipsis: { tooltip: true } },
    { key: 'eFileLook', title: '内发:电子文件-查阅', align: 'center', ellipsis: { tooltip: true } },
    { key: 'eFileLookAndDownload', title: '内发:电子文件-查阅/下载', align: 'center', ellipsis: { tooltip: true } },
    { key: 'paperDocumentOnceDownload', title: '内发:纸质文件-一次下载', align: 'center', ellipsis: { tooltip: true } },
    { key: 'todo', title: '操作', align: 'center', fixed: 'right', width: 100 }
]);

const tableData = ref<TableRow[]>([]);
const checkedMap = ref<
    Record<
        string,
        Array<{
            filePermission: number;
            receivedBy: string[];
        }>
    >
>({});
const checkedRowKeys = ref<string[]>([]);
const loading = ref(false);

const loadData = async () => {
    loading.value = true;
    try {
        // 通过id获取数据
        const res = await $apis.nebula.api.v1.issuanceApplication.getDistributeInventory(props.id);
        console.log('API响应:', res);

        // 根据实际返回的数据结构获取数据
        let rawData = [];
        if (res && res.data && res.data.data && Array.isArray(res.data.data)) {
            rawData = res.data.data;
        } else if (res && res.data && Array.isArray(res.data)) {
            rawData = res.data;
        } else {
            console.warn('API返回的数据格式不正确:', res);
            rawData = [];
        }

        // 直接使用新的数据结构
        tableData.value = rawData;

        initCheckedMap();
    } catch (error) {
        console.error('获取数据失败:', error);
        window.$message.error('获取数据失败');
        tableData.value = [];
    } finally {
        loading.value = false;
    }
};

function initCheckedMap() {
    checkedMap.value = {};
    for (const row of tableData.value) {
        checkedMap.value[row.fileId] = [];
    }
    // 回填 modelValue
    for (const item of props.modelValue || []) {
        if (checkedMap.value[item.inventoryId]) {
            checkedMap.value[item.inventoryId] = [...item.permissions];
        }
    }
    // 初始化时更新行选择框状态
    updateRowSelection();
}

// 更新行选择框状态
function updateRowSelection() {
    const newCheckedRowKeys = tableData.value
        .filter((row) => {
            const checked = checkedMap.value[row.fileId] || [];
            // 检查该行是否有任何权限被选中
            return checked.some((permission) => permission.receivedBy && permission.receivedBy.length > 0);
        })
        .map((row) => row.fileId);

    checkedRowKeys.value = newCheckedRowKeys;
}

function handlePermissionChange(row: TableRow, permissionKey: string, val: (string | number)[]) {
    let filePermission: number;

    switch (permissionKey) {
        case 'eFileLook':
            filePermission = 1;
            break;
        case 'eFileLookAndDownload':
            filePermission = 2;
            break;
        case 'paperDocumentOnceDownload':
            filePermission = 3;
            break;
        case 'eFileOnceDownload':
            filePermission = 4;
            break;
        default:
            return;
    }

    const existingIndex = checkedMap.value[row.fileId].findIndex((p) => p.filePermission === filePermission);

    if (val.length > 0) {
        const permission = {
            filePermission,
            receivedBy: val.map(String)
        };

        if (existingIndex >= 0) {
            checkedMap.value[row.fileId][existingIndex] = permission;
        } else {
            checkedMap.value[row.fileId].push(permission);
        }
    } else if (existingIndex >= 0) {
        checkedMap.value[row.fileId].splice(existingIndex, 1);
    }

    // 用户手动选择人员时，更新行选择框状态
    updateRowSelection();
    emitBackData();
}

function handleSelectionChange(keys: (string | number)[]) {
    const stringKeys = keys.map(String);

    // 找出状态发生变化的行
    const changedRows = tableData.value.filter((row) => {
        const isChecked = stringKeys.includes(row.fileId);
        const wasChecked = checkedRowKeys.value.includes(row.fileId);
        return isChecked !== wasChecked;
    });

    // 只处理状态发生变化的行
    for (const row of changedRows) {
        const isChecked = stringKeys.includes(row.fileId);
        const wasChecked = checkedRowKeys.value.includes(row.fileId);

        if (isChecked && !wasChecked) {
            // 用户选中行时，全选该行所有可用的权限（只选择 status === 1 的项）
            const permissions = [];

            // eFileLook
            const eFileLookUsers = row.eFileLook?.receivedBy?.filter((i) => i.status === 1).map((i) => i.userId) || [];
            if (eFileLookUsers.length > 0) {
                permissions.push({ filePermission: 1, receivedBy: eFileLookUsers });
            }

            // eFileLookAndDownload
            const eFileLookAndDownloadUsers =
                row.eFileLookAndDownload?.receivedBy?.filter((i) => i.status === 1).map((i) => i.userId) || [];
            if (eFileLookAndDownloadUsers.length > 0) {
                permissions.push({ filePermission: 2, receivedBy: eFileLookAndDownloadUsers });
            }

            // paperDocumentOnceDownload
            const paperDocumentOnceDownloadUsers =
                row.paperDocumentOnceDownload?.receivedBy?.filter((i) => i.status === 1).map((i) => i.userId) || [];
            if (paperDocumentOnceDownloadUsers.length > 0) {
                permissions.push({ filePermission: 3, receivedBy: paperDocumentOnceDownloadUsers });
            }

            // eFileOnceDownload
            const eFileOnceDownloadUsers =
                row.eFileOnceDownload?.receivedBy?.filter((i) => i.status === 1).map((i) => i.userId) || [];
            if (eFileOnceDownloadUsers.length > 0) {
                permissions.push({ filePermission: 4, receivedBy: eFileOnceDownloadUsers });
            }

            checkedMap.value[row.fileId] = permissions;
        } else if (!isChecked && wasChecked) {
            // 用户取消选中行时，清空该行的所有选择
            checkedMap.value[row.fileId] = [];
        }
    }

    // 更新行选择框状态
    checkedRowKeys.value = stringKeys;
    emitBackData();
}

function getCheckedUsers(fileId: string, filePermission: number): string[] {
    const permissions = checkedMap.value[fileId] || [];
    const permission = permissions.find((p) => p.filePermission === filePermission);
    return permission ? permission.receivedBy : [];
}

function emitBackData() {
    const backData = Object.entries(checkedMap.value)
        .filter(([, permissions]) => permissions.length > 0)
        .map(([inventoryId, permissions]) => {
            // 找到对应的表格行数据
            const rowData = tableData.value.find((row) => row.fileId === inventoryId);

            // 转换权限数据，包含人员名字
            const permissionsWithNames = permissions.map((permission) => {
                const filePermission = permission.filePermission;
                let fileForm;
                let userList: Array<{ userId: string; nickname: string; status: number }> = [];

                // 根据权限类型获取对应的用户列表
                switch (filePermission) {
                    case 1:
                        fileForm = rowData?.eFileLook?.fileForm;
                        userList = rowData?.eFileLook?.receivedBy || [];
                        break;
                    case 2:
                        fileForm = rowData?.eFileLookAndDownload?.fileForm;
                        userList = rowData?.eFileLookAndDownload?.receivedBy || [];
                        break;
                    case 3:
                        fileForm = rowData?.paperDocumentOnceDownload?.fileForm;
                        userList = rowData?.paperDocumentOnceDownload?.receivedBy || [];
                        break;
                }

                // 过滤出选中的用户，并转换为包含名字的格式
                const selectedUsers = userList
                    .filter((user) => permission.receivedBy.includes(user.userId))
                    .map((user) => ({
                        userId: user.userId,
                        nickname: user.nickname
                    }));

                return {
                    fileForm,
                    filePermission,
                    receivedBy: permission.receivedBy,
                    receivedByNames: selectedUsers
                };
            });

            return {
                inventoryId: rowData?.id,
                permissions: permissionsWithNames,
                // 添加文件信息
                fileInfo: {
                    fileName: rowData?.fileName || '',
                    number: rowData?.number || '',
                    version: rowData?.version || ''
                }
            };
        });
    console.log(backData);
    emit('update:modelValue', backData);
}

onMounted(() => {
    loadData();
});

const showRecycleDialog = (row: any) => {
    $alert.dialog({
        title: '回收记录',
        content: import('../models/recycle-record.vue'),
        width: '60%',
        props: { id: row.id }
    });
};
</script>

<style scoped lang="less">
</style>
