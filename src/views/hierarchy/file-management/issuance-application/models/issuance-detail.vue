<template>
    <alert-content :showDefaultButtons="false">
        <n-space vertical size="large">
            <n-descriptions :column="2" size="large" label-placement="left">
                <n-descriptions-item label="发放人">{{ row.applicant }}</n-descriptions-item>
                <n-descriptions-item label="发放日期">{{
                    dayjs(row.applyDate).format('YYYY-MM-DD')
                }}</n-descriptions-item>
                <n-descriptions-item label="发放类型">{{
                    issuanceTypeOptions.find((item) => item.value === row.distributeType)?.label
                }}</n-descriptions-item>
                <n-descriptions-item label="文件类型">{{
                    fileTypeOptions.find((item) => item.value === row.fileType)?.label
                }}</n-descriptions-item>
                <n-descriptions-item label="文件类别">{{ row.fileCategory }}</n-descriptions-item>
                <n-descriptions-item label="发放原因">
                    <n-ellipsis>{{ row.reason }}</n-ellipsis>
                </n-descriptions-item>
                <n-descriptions-item label="其他原因" v-if="row.otherReason">{{ row.otherReason }}</n-descriptions-item>
                <n-descriptions-item label="希望发放日期" v-if="row.wishDistributeDate">{{
                    dayjs(row.wishDistributeDate).format('YYYY-MM-DD')
                }}</n-descriptions-item>
            </n-descriptions>
            <p style="font-size: 15px">发放清单</p>
            <n-search-table-page
                :data-table-props="{
                    columns,
                    data: tableData,
                    size: 'small',
                    bordered: true,
                    pagination: false,
                    scrollX: 2000
                }"
                :search-props="{
                    show: false
                }"
                :table-props="{
                    showPagination: false
                }"
            >
                <template #table_eFileLook="{ row }">
                    <div style="line-height: 1.5">
                        <template v-for="(item, index) in row.eFileLook?.receivedBy || []" :key="index">
                            <span v-if="item.status <= 2">{{ item.nickname }}</span>
                            <n-tooltip v-else trigger="hover" placement="top">
                                <template #trigger>
                                    <span style="color: #1890ff; text-decoration: underline; cursor: pointer">
                                        {{ item.nickname }}
                                    </span>
                                </template>
                                <span>回收日期：{{ formatRecycleDate(item.recycleDate) }}</span>
                            </n-tooltip>
                            <span v-if="index < (row.eFileLook?.receivedBy?.length || 0) - 1">，</span>
                        </template>
                        <span
                            v-if="row.recipient && row.eFileLook?.receivedBy?.length !== 0"
                            style="margin-left: 8px"
                            >{{ '(' + row.recipient + ')' }}</span
                        >
                    </div>
                </template>
                <template #table_eFileLookAndDownload="{ row }">
                    <div style="line-height: 1.5">
                        <template v-for="(item, index) in row.eFileLookAndDownload?.receivedBy || []" :key="index">
                            <span v-if="item.status <= 2">{{ item.nickname }}</span>
                            <n-tooltip v-else trigger="hover" placement="top">
                                <template #trigger>
                                    <span style="color: #1890ff; text-decoration: underline; cursor: pointer">
                                        {{ item.nickname }}
                                    </span>
                                </template>
                                <span>回收日期：{{ formatRecycleDate(item.recycleDate) }}</span>
                            </n-tooltip>
                            <span v-if="index < (row.eFileLookAndDownload?.receivedBy?.length || 0) - 1">，</span>
                        </template>
                        <span
                            v-if="row.recipient && row.eFileLookAndDownload?.receivedBy?.length !== 0"
                            style="margin-left: 8px"
                            >{{ '(' + row.recipient + ')' }}</span
                        >
                    </div>
                </template>
                <template #table_paperDocumentOnceDownload="{ row }">
                    <div style="line-height: 1.5">
                        <template v-for="(item, index) in row.paperDocumentOnceDownload?.receivedBy || []" :key="index">
                            <span v-if="item.status <= 2">{{ item.nickname }}</span>
                            <n-tooltip v-else trigger="hover" placement="top">
                                <template #trigger>
                                    <span style="color: #1890ff; text-decoration: underline; cursor: pointer">
                                        {{ item.nickname }}
                                    </span>
                                </template>
                                <span>回收日期：{{ formatRecycleDate(item.recycleDate) }}</span>
                            </n-tooltip>
                            <span v-if="index < (row.paperDocumentOnceDownload?.receivedBy?.length || 0) - 1">，</span>
                        </template>
                        <span
                            v-if="row.recipient && row.paperDocumentOnceDownload?.receivedBy?.length != 0"
                            style="margin-left: 8px"
                            >{{ '(' + row.recipient + ')' }}</span
                        >
                    </div>
                </template>
                <template #table_eFileOnceDownload="{ row }">
                    <div style="line-height: 1.5">
                        <template v-for="(item, index) in row.eFileOnceDownload?.receivedBy || []" :key="index">
                            <span v-if="item.status <= 2">{{ item.nickname }}</span>
                            <n-tooltip v-else trigger="hover" placement="top">
                                <template #trigger>
                                    <span style="color: #1890ff; text-decoration: underline; cursor: pointer">
                                        {{ item.nickname }}
                                    </span>
                                </template>
                                <span>回收日期：{{ formatRecycleDate(item.recycleDate) }}</span>
                            </n-tooltip>
                            <span v-if="index < (row.eFileOnceDownload?.receivedBy?.length || 0) - 1">，</span>
                        </template>
                        <span
                            v-if="row.recipient && row.eFileOnceDownload?.receivedBy?.length !== 0"
                            style="margin-left: 8px"
                            >{{ '(' + row.recipient + ')' }}</span
                        >
                    </div>
                </template>
                <template #table_action="{ row }">
                    <n-button @click="showRecycleDialog(row)" size="tiny" type="primary">回收记录</n-button>
                </template>
            </n-search-table-page>
            <span v-if="row.status != 1" style="font-weight: bold" class="my-20px">发放审批记录</span>
            <div class="right w-300px p-15px">
                <bs-timeline :data="workflowDetail" backgroundColor="#fff"></bs-timeline>
            </div>
        </n-space>
    </alert-content>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import dayjs from 'dayjs';
const props = defineProps({
    row: { type: Object, default: () => ({}) }
});
const workflowDetail = ref<any>([]);
const tableData = ref<any[]>([]);
const issuanceTypeOptions = [
    { label: '内部发放', value: 1 },
    { label: '外部发放', value: 2 }
];
const fileTypeOptions = [
    { label: '内部文件', value: 1 },
    { label: '外部文件', value: 2 }
];

const getWorkflowDetail = async (id: string) => {
    const res = await api.sass.api.v1.workflow.workflow.detail(id);

    if (!res.data) {
        throw new Error('获取审批详情失败');
    }
    workflowDetail.value = res.data.nodes.filter((item: any) => item.status != 'notStarted');
};

const columns = [
    {
        title: '序号',
        key: 'index',
        width: 60,
        render: (_: any, index: number) => {
            return `${index + 1}`;
        }
    },
    { title: '文件名称', key: 'fileName', width: 100, fixed: 'left' as const },
    { title: '文件编号', key: 'number', width: 100 },
    { title: '版本/版次', key: 'version', width: 100 },
    {
        title: '内发：电子文件-查阅',
        key: 'eFileLook',
        width: 200
    },
    {
        title: '内发：电子文件-查阅/下载',
        key: 'eFileLookAndDownload',
        width: 200
    },
    {
        title: '内发：纸质文件-一次下载',
        key: 'paperDocumentOnceDownload',
        width: 200
    },
    {
        title: '外发：电子文件-一次下载',
        key: 'eFileOnceDownload',
        width: 200
    },
    {
        title: '操作',
        key: 'action',
        width: 80,
        fixed: 'right' as const,
        align: 'center' as const
    }
];

const formatRecycleDate = (date: string | null) => {
    if (!date) return '暂无';
    return dayjs(date).format('YYYY-MM-DD');
};

onMounted(async () => {
    const res = await $apis.nebula.api.v1.issuanceApplication.getDistributeInventory(props.row.id);
    tableData.value = res.data.data;
    if (props.row.status != 1) {
        getWorkflowDetail(props.row.workflowId);
    }
});

const showRecycleDialog = (row: any) => {
    $alert.dialog({
        title: '回收记录',
        content: import('./recycle-record.vue'),
        width: '60%',
        props: {
            id: row.id
        }
    });
};
</script>
<style scoped>
.n-step-description {
    display: flex;
    justify-content: space-between;
    align-items: center;
}
</style>
