<template>
    <alert-content :on-default-save="onSubmit">
        <n-form
            class="mt-2"
            ref="formRef"
            label-align="right"
            :model="formData"
            :rules="rules"
            label-placement="left"
            require-mark-placement="left"
            :label-width="80"
            :show-feedback="false"
        >
            <n-grid :cols="24" :y-gap="10" :x-gap="10">
                <n-form-item-gi label="处置人:" :span="12">
                    <n-input disabled :value="userInfo.name" />
                </n-form-item-gi>
                <n-form-item-gi label="处置日期" :span="12">
                    <n-input disabled :value="userInfo.today" />
                </n-form-item-gi>
                <n-form-item-gi label="发放类型" :span="12">
                    <n-select v-model:value="formData.distributeType" disabled :options="issuanceTypeOptions" />
                </n-form-item-gi>
                <n-form-item-gi label="文件类型" :span="12">
                    <n-select v-model:value="formData.fileType" disabled :options="fileTypeOptions" />
                </n-form-item-gi>
                <n-form-item-gi label="文件类别" :span="12">
                    <n-input disabled :value="row.fileCategory" />
                </n-form-item-gi>
                <n-form-item-gi label="发放原因" :span="12">
                    <n-input disabled :value="row.reason" />
                </n-form-item-gi>
                <n-form-item-gi label="其他原因" :span="24" v-if="row.reason === '其他'">
                    <n-input v-model:value="formData.otherReason" />
                </n-form-item-gi>
                <n-form-item-gi label="处置方式" path="disposalReason" :span="12">
                    <n-input
                        v-model:value="formData.disposalReason"
                        maxlength="50"
                        placeholder="请输入处置方式"
                        show-count
                    />
                </n-form-item-gi>
                <n-form-item-gi path="disposalList" :span="24">
                    <div class="flex-v w-100%">
                        <span class="my-10px text-14px required-field">纸质文件处置清单</span>
                        <paper-dispose-list-table v-model="formData.disposalList" :id="id" />
                    </div>
                </n-form-item-gi>
            </n-grid>
        </n-form>
    </alert-content>
</template>

<script setup lang="ts">
import { FormInst, FormRules } from 'naive-ui';
import AlertContent from '@/components/alert-content.vue';
import PaperDisposeListTable from '../components/paper-dispose-list-table.vue';
import useStore from '@/store/modules/main';
const { row,id } = defineProps<{ row: any,id:string }>();
const store = useStore();
const userInfo = {
    name: store.userInfo.nickname,
    today: new Date().toISOString().slice(0, 10)
};
const formRef = ref<FormInst>();

const formData = reactive({
    disposalReason: '',
    disposalList: [],
    distributeType: row.distributeType,
    fileType: row.fileType,
    otherReason: row.otherReason || ''
});

const issuanceTypeOptions = [
    { label: '内部发放', value: 1 },
    { label: '外部发放', value: 2 }
];
const fileTypeOptions = [
    { label: '内部文件', value: 1 },
    { label: '外部文件', value: 2 }
];

const rules = computed<FormRules>(() => ({
    disposalReason: [{ required: true, message: '请输入处置方式', trigger: ['blur', 'input'] }],
    disposalList: [
        {
            required: true,
            trigger: 'blur',
            validator: () => {
                if (!formData.disposalList.length) {
                    return new Error('请至少选择一个文件的人员进行处置');
                }
                return true;
            }
        }
    ]
}));

const onSubmit = async () => {
    // 构建完整的数据对象，包含所有显示字段
    const completeData = {
        // 表单数据
        ...formData,
        // 处置相关信息
        disposalDate: new Date().getTime(),
        distributeId: row.id,
        // 处置人信息
        disposer: {
            nickname: userInfo.name,
            disposeDate: userInfo.today
        },
        // 发放申请信息
        distributeData: {
            distributeType: row.distributeType,
            distributeTypeText: issuanceTypeOptions.find((opt) => opt.value === row.distributeType)?.label || '',
            fileType: row.fileType,
            fileTypeText: fileTypeOptions.find((opt) => opt.value === row.fileType)?.label || '',
            fileCategory: row.fileCategory,
            reason: row.reason,
            otherReason: row.otherReason || ''
        }
    };

    const data = JSON.stringify({
        businessId: 'FILE_DISPOSAL',
        version: '1.0.0',
        data: completeData
    });
    await formRef.value
        ?.validate()
        .then(async () => {
            await new Promise((resolve) => {
                window.$dialog.warning({
                    title: '确认提示',
                    content: '确认后将发起审批流程，是否确认？',
                    positiveText: '确认',
                    negativeText: '取消',
                    onPositiveClick: async () => {
                        await $hooks.useApprovalProcess('FILE_DISPOSAL', data);
                        window.$message.success('处置流程已发起');
                        resolve(true);
                    }
                });
            });
        })
        .catch((errors) => {
            if (errors && errors[0] && errors[0][0]) {
                window.$message.error(errors[0][0].message);
            } else {
                window.$message.error('请完善表单信息');
            }
            return Promise.reject();
        });
};
</script>

<style scoped lang="less">
/* 添加必填星号样式 */
.required-field::before {
    content: '*';
    color: var(--n-asterisk-color);
    margin-right: 4px;
}
</style>
