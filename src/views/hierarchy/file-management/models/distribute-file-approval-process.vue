<template>
    <div class="approval-process">
        <!-- 兼容老逻辑：单个审批流程 -->
        <div v-if="sections.length === 1 && !props.row" class="approval-section">
            <div class="approval-title">
                <span>{{ title || sections[0].title }}</span>
                <n-tag round :bordered="false" size="small" :type="sections[0].tagType" class="ml-16px font-normal">{{
                    sections[0].tag
                }}</n-tag>
            </div>
            <div class="approval-content">
                <div class="approval-row" v-for="(item, i) in sections[0].approvers" :key="i">
                    <div class="label">{{ item.label }}</div>
                    <div class="value">{{ item.name }}</div>
                    <div class="label">{{ item.dateLabel }}</div>
                    <div class="value">{{ item.date }}</div>
                </div>
            </div>
        </div>
        
        <!-- 新逻辑：多个审批流程 -->
        <div v-else>
            <div v-for="(section, index) in sections" :key="index" class="approval-section">
                <div class="approval-title">
                    <span>{{ section.title }}</span>
                    <n-tag round :bordered="false" size="small" :type="section.tagType" class="ml-16px font-normal">{{
                        section.tag
                    }}</n-tag>
                </div>
                <div class="approval-content">
                    <div class="approval-row" v-for="(item, i) in section.approvers" :key="i">
                        <div class="label">{{ item.label }}</div>
                        <div class="value">{{ item.name }}</div>
                        <div class="label">{{ item.dateLabel }}</div>
                        <div class="value">{{ item.date }}</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import dayjs from 'dayjs';

const props = withDefaults(
    defineProps<{
        flowId: string;
        title?: string;
        row?: any;
    }>(),
    {
        flowId: ''
    }
);
const emit = defineEmits(['update:flowId']);
const { flowId } = useVModels(props, emit);

type ApproverStatus = 'passed' | 'rejected' | 'pending';

interface FlowData {
    flowName: string;
    nodes: Node[];
    formContent: string; // JSON 字符串
}

interface Node {
    nodeName: string;
    signingKind: 'or' | 'and';
    updatedAt: number;
    approvers: Approver[];
}

interface Approver {
    approverId?: string;
    approverNickname: string;
    status: ApproverStatus;
    updatedAt: number;
}

interface TransformedApprover {
    label: string;
    name: string;
    dateLabel: string;
    date: string;
}

interface TransformedData {
    title: string;
    tag: string; // 发放 / 回收 / 处置
    tagType: 'default' | 'error' | 'success' | 'warning' | 'primary' | 'info'; // success / warning / error / default
    approvers: TransformedApprover[];
}

function transformFlowData(data: FlowData): TransformedData {
    const title = data.flowName;
    const approvers: TransformedApprover[] = [];
    const nodes = data.nodes || [];
    const nodeCount = nodes.length;

    // 解析 businessId
    let businessId = '';
    try {
        const parsed = JSON.parse(data.formContent);
        businessId = parsed.businessId || parsed?.data?.businessId || '';
    } catch (e) {
        console.warn('formContent JSON 解析失败:', e);
    }

    // tag 与 tagType 映射
    const tagMap: Record<string, { tag: string; tagType: 'default' | 'error' | 'success' | 'warning' | 'primary' | 'info' }> = {
        FILE_GRANT: { tag: '发放', tagType: 'success' },
        FILE_RECLAIM: { tag: '回收', tagType: 'warning' },
        FILE_DISPOSAL: { tag: '处置', tagType: 'error' }
    };
    const tagInfo = tagMap[businessId] || { tag: '未知', tagType: 'default' as const };

    const formatDate = (ts: number) => dayjs(ts).format('YYYY-MM-DD HH:mm:ss');

    const extractForNode = (node: Node, roleLabel: string, dateLabel: string): TransformedApprover[] => {
        const list: TransformedApprover[] = [];

        if (node.signingKind === 'or') {
            const passed = node.approvers.find((a) => a.status === 'passed');
            if (passed) {
                list.push({
                    label: roleLabel,
                    name: passed.approverNickname,
                    dateLabel,
                    date: formatDate(passed.updatedAt)
                });
            }
        } else if (node.signingKind === 'and') {
            node.approvers
                .filter((a) => a.status === 'passed')
                .forEach((passed) => {
                    list.push({
                        label: roleLabel,
                        name: passed.approverNickname,
                        dateLabel,
                        date: formatDate(passed.updatedAt)
                    });
                });
        }

        return list;
    };

    nodes.forEach((node, idx) => {
        const isLast = idx === nodeCount - 1;

        if (nodeCount === 1) {
            approvers.push(...extractForNode(node, '审核人', '审核日期'));
            approvers.push(...extractForNode(node, '批准人', '批准日期'));
        } else {
            if (!isLast) {
                approvers.push(...extractForNode(node, '审核人', '审核日期'));
            } else {
                approvers.push(...extractForNode(node, '批准人', '批准日期'));
            }
        }
    });

    return {
        title,
        tag: tagInfo.tag,
        tagType: tagInfo.tagType,
        approvers
    };
}

// 转换row数据为审批信息
function transformRowData(row: any): TransformedData[] {
    const sections: TransformedData[] = [];
    
    // 发放审批信息
    if (row.distributeApprovalInfo && 
        ((row.distributeApprovalInfo.approvers && row.distributeApprovalInfo.approvers.length > 0) ||
         (row.distributeApprovalInfo.auditors && row.distributeApprovalInfo.auditors.length > 0))) {
        sections.push(createSectionFromApprovalInfo(row.distributeApprovalInfo, '发放', 'success'));
    }
    
    // 回收审批信息
    if (row.recycleApprovalInfo && 
        ((row.recycleApprovalInfo.approvers && row.recycleApprovalInfo.approvers.length > 0) ||
         (row.recycleApprovalInfo.auditors && row.recycleApprovalInfo.auditors.length > 0))) {
        sections.push(createSectionFromApprovalInfo(row.recycleApprovalInfo, '回收', 'warning'));
    }
    
    // 处置审批信息
    if (row.disposalApprovalInfo && 
        ((row.disposalApprovalInfo.approvers && row.disposalApprovalInfo.approvers.length > 0) ||
         (row.disposalApprovalInfo.auditors && row.disposalApprovalInfo.auditors.length > 0))) {
        sections.push(createSectionFromApprovalInfo(row.disposalApprovalInfo, '处置', 'error'));
    }
    
    return sections;
}

// 从审批信息创建章节
function createSectionFromApprovalInfo(approvalInfo: any, type: string, tagType: 'default' | 'error' | 'success' | 'warning' | 'primary' | 'info'): TransformedData {
    const approvers: TransformedApprover[] = [];
    const formatDate = (ts: number) => dayjs(ts).format('YYYY-MM-DD HH:mm:ss');
    
    // 添加审批人
    if (approvalInfo.approvers && approvalInfo.approvers.length > 0) {
        approvalInfo.approvers.forEach((approver: any) => {
            if (approver.passedDate) {
                approvers.push({
                    label: '审核人',
                    name: approver.userNickname,
                    dateLabel: '审核日期',
                    date: formatDate(approver.passedDate)
                });
            }
        });
    }
    
    // 添加批准人
    if (approvalInfo.auditors && approvalInfo.auditors.length > 0) {
        approvalInfo.auditors.forEach((auditor: any) => {
            if (auditor.passedDate) {
                approvers.push({
                    label: '批准人',
                    name: auditor.userNickname,
                    dateLabel: '批准日期',
                    date: formatDate(auditor.passedDate)
                });
            }
        });
    }
    
    return {
        title: `${props.title || '审批流程'} - ${type}`,
        tag: type,
        tagType: tagType,
        approvers: approvers
    };
}

// 获取审批流程详情
/**  如果有row
 * row.distributeApprovalInfo 发放审批记录 approvers审批人 auditors批准人
 * row.recycleApprovalInfo 回收审批记录 approvers审批人 auditors批准人
 * row.disposalApprovalInfo 处置审批记录 approvers审批人 auditors批准人
 */
const sections = ref<TransformedData[]>([]);
const getWorkflowDetail = async (id: string) => {
    if (props.row) {
        // 新的数据结构：处理 row 中的多个审批信息
        const rowSections = transformRowData(props.row);
        if (rowSections.length > 0) {
            sections.value = rowSections;
        } else {
            // 如果 row 没有审批信息，但有 flowId，则使用老逻辑
            if (id) {
                const res = await window.api.sass.api.v1.workflow.workflow.detail(id);
                if (res.data) {
                    sections.value = [transformFlowData(res.data)];
                }
            }
        }
    } else if (id) {
        // 直接通过 flowId 获取审批详情
        const res = await window.api.sass.api.v1.workflow.workflow.detail(id);
        if (!res.data) {
            throw new Error('获取审批详情失败');
        }
        sections.value = [transformFlowData(res.data)];
    }
};

// const sections = computed(() => {
//     if (type.value === 1) {
//         return [
//             {
//                 title: '内发：电子文件-查询',
//                 action: 'grant',
//                 approvers: [
//                     { label: '审核人', name: '张三', dateLabel: '审核日期', date: '2025-05-02' },
//                     { label: '审核人', name: '张三', dateLabel: '审核日期', date: '2025-05-02' },
//                     { label: '批准人', name: '张三', dateLabel: '批准日期', date: '2025-05-02' }
//                 ]
//             },
//             {
//                 title: '内发：电子文件-查询',
//                 action: 'recycle',
//                 approvers: [
//                     { label: '审核人', name: '张三', dateLabel: '审核日期', date: '2025-05-02' },
//                     { label: '审核人', name: '张三', dateLabel: '审核日期', date: '2025-05-02' },
//                     { label: '批准人', name: '张三', dateLabel: '批准日期', date: '2025-05-02' }
//                 ]
//             },
//             {
//                 title: '内发：电子文件-查询/下载',
//                 action: 'grant',
//                 approvers: [
//                     { label: '审核人', name: '张三', dateLabel: '审核日期', date: '2025-05-02' },
//                     { label: '审核人', name: '张三', dateLabel: '审核日期', date: '2025-05-02' },
//                     { label: '批准人', name: '张三', dateLabel: '批准日期', date: '2025-05-02' }
//                 ]
//             },
//             {
//                 title: '内发：电子文件-查询/下载',
//                 action: 'recycle',
//                 approvers: [
//                     { label: '审核人', name: '张三', dateLabel: '审核日期', date: '2025-05-02' },
//                     { label: '审核人', name: '张三', dateLabel: '审核日期', date: '2025-05-02' },
//                     { label: '批准人', name: '张三', dateLabel: '批准日期', date: '2025-05-02' }
//                 ]
//             },
//             {
//                 title: '外发：电子文件-一次下载',
//                 action: 'grant',
//                 approvers: [
//                     { label: '审核人', name: '张三', dateLabel: '审核日期', date: '2025-05-02' },
//                     { label: '审核人', name: '张三', dateLabel: '审核日期', date: '2025-05-02' },
//                     { label: '批准人', name: '张三', dateLabel: '批准日期', date: '2025-05-02' }
//                 ]
//             }
//         ];
//     } else if (type.value === 2) {
//         return [
//             {
//                 title: '内发：纸质文件-一次下载变更记录',
//                 action: 'grant',
//                 approvers: [
//                     { label: '审核人', name: '张三', dateLabel: '审核日期', date: '2025-05-02' },
//                     { label: '审核人', name: '张三', dateLabel: '审核日期', date: '2025-05-02' },
//                     { label: '批准人', name: '张三', dateLabel: '批准日期', date: '2025-05-02' }
//                 ]
//             },
//             {
//                 title: '内发：纸质文件-一次下载变更记录',
//                 action: 'recycle',
//                 approvers: [
//                     { label: '审核人', name: '张三', dateLabel: '审核日期', date: '2025-05-02' },
//                     { label: '审核人', name: '张三', dateLabel: '审核日期', date: '2025-05-02' },
//                     { label: '批准人', name: '张三', dateLabel: '批准日期', date: '2025-05-02' }
//                 ]
//             }
//         ];
//     } else {
//         return [
//             {
//                 title: '借阅记录',
//                 action: 'grant',
//                 approvers: [
//                     { label: '审核人', name: '张三', dateLabel: '审核日期', date: '2025-05-02' },
//                     { label: '审核人', name: '张三', dateLabel: '审核日期', date: '2025-05-02' },
//                     { label: '批准人', name: '张三', dateLabel: '批准日期', date: '2025-05-02' }
//                 ]
//             },
//             {
//                 title: '借阅记录',
//                 action: 'recycle',
//                 approvers: [
//                     { label: '审核人', name: '张三', dateLabel: '审核日期', date: '2025-05-02' },
//                     { label: '审核人', name: '张三', dateLabel: '审核日期', date: '2025-05-02' },
//                     { label: '批准人', name: '张三', dateLabel: '批准日期', date: '2025-05-02' }
//                 ]
//             }
//         ];
//     }
// });

onMounted(() => {
    getWorkflowDetail(flowId.value);
});
</script>

<style scoped lang="less">
.approval-process {
    font-size: 15px;
    .approval-section {
        margin-bottom: 24px;
    }
    .approval-title {
        font-weight: bold;
        margin-bottom: 8px;
        .action {
            margin-left: 16px;
        }
    }
    .approval-content {
        display: flex;
        flex-direction: column;
        gap: 2px;
    }
    .approval-row {
        display: flex;
        gap: 12px;
        margin-bottom: 2px;
    }
    .label {
        min-width: 56px;
        color: #333;
    }
    .value {
        min-width: 60px;
        color: #000;
    }
}
</style>
