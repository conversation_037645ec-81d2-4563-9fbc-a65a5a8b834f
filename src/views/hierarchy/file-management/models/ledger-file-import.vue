<template>
    <alert-content :on-default-save="submit">
        <n-alert :bordered="false" type="warning" :show-icon="false">
            {{`提示：台账中的${type === 'book' ? '书籍名称' : '文件名称'}需和上传文件的名称保持一致，否则会导致导入失败!`}}
        </n-alert>
        <n-form
            ref="formRef"
            :model="data"
            :rules="rules"
            label-placement="left"
            require-mark-placement="left"
            :label-width="80"
            :show-feedback="false"
        >
            <n-form-item label="下载模版" class="mt-10px">
                <n-button type="primary" size="small" @click="handleDownloadTemplate">点击下载</n-button>
            </n-form-item>
            <n-form-item label="上传台账" path="ledger">
                <minio-upload
                    class="w-full mt-10px"
                    v-model:file-list="data.ledger"
                    :upload-props="{ max: 1, accept: '.xls,.xlsx' }"
                >
                    <template #drag>
                        <div class="w-full h-full flex-v justify-center items-center p-10px">
                            <img :src="uploadFileImg" class="w-40px h-40px mt-10px" />
                            <span class="text-14px mt-10px">点击或者拖动文件到该区域来上传</span>
                            <span class="text-12px c-#838383 mt-10px">只支持上传.xls、.xlsx文件</span>
                        </div>
                    </template>
                </minio-upload>
            </n-form-item>
            <n-form-item label="上传文件">
                <minio-upload
                    class="w-full mt-10px"
                    v-model:file-list="data.fileList"
                    :upload-props="{ accept: '.pdf', multiple: true }"
                >
                    <template #drag>
                        <div class="w-full h-full flex-v justify-center items-center p-10px">
                            <img :src="uploadFileImg" class="w-40px h-40px mt-10px" />
                            <span class="text-14px mt-10px">点击或者拖动文件到该区域来上传</span>
                            <span class="text-12px c-#838383 mt-10px">只支持上传.pdf文件</span>
                        </div>
                    </template>
                </minio-upload>
            </n-form-item>
        </n-form>
    </alert-content>
</template>

<script setup lang="ts">
import { FormRules } from 'naive-ui';
import uploadFileImg from '@/assets/images/file/upload-file.webp';

const props = withDefaults(
    defineProps<{
        type?: string; // book：书籍, internal：内部文件, external：外部文件
    }>(),
    {
        type: ''
    }
);
const emit = defineEmits(['update:type']);
const { type } = useVModels(props, emit);
const data = ref<any>({
    ledger: [],
    fileList: []
});
const rules: FormRules = {
    ledger: { required: true, message: '请上传台账文件', trigger: 'change', type: 'array' }
    // fileList: { required: true, message: '请上传文件', trigger: 'change', type: 'array' }
};

const formRef = ref();

const submit = async () => {
    try {
        await formRef.value?.validate();
    } catch (err: any) {
        // 用来提示表单必填错误
        window.$message.error(err[0][0].message);
        return Promise.reject();
    }
    const { ledger, fileList } = data.value;
    // 获取字典信息
    const {
        data: {
            data: [dictData]
        }
    } = await api.sass.api.v1.dict.get('file_business_dictionary');
    let dictInfo: {
        authDictionaryId?: string;
        domainDictionaryId?: string;
        typeDictionaryId: string;
    } = {
        typeDictionaryId: ''
    };

    switch (type.value) {
        case 'external':
            dictInfo = {
                authDictionaryId: dictData.extra.authDictionaryId,
                domainDictionaryId: dictData.extra.domainDictionaryId,
                typeDictionaryId: dictData.extra.fileCategory.external
            };
            break;
        case 'internal':
            dictInfo = {
                typeDictionaryId: dictData.extra.fileCategory.internal
            };
            break;
        case 'book':
            dictInfo = {
                typeDictionaryId: dictData.extra.fileCategory.book
            };
            break;
    }
    // 模块类型映射
    const moduleTypes: Record<string, number> = { book: 1, internal: 2, external: 3 };

    await new Promise((resolve) => {
        window.$dialog.warning({
            title: '提示',
            content: '确认后将新增文件，是否确认？',
            positiveText: '确认',
            negativeText: '取消',
            onPositiveClick: async () => {
                // 执行导入
                $apis.nebula.api.v1.internal
                    .import({
                        ...dictInfo,
                        mainFileId: ledger[0]?.response?.id,
                        listFileIds: fileList.map((item: any) => item.response.id),
                        moduleType: moduleTypes[type.value]
                    })
                    .then(() => {
                        window.$message.success('导入成功');
                        resolve(true);
                    })
                    .catch(() => {
                        window.$dialog.destroyAll();
                    });
            }
        });
    });
};

const handleDownloadTemplate = async () => {
    const dict = await api.sass.api.v1.dict.get('file_import_template');

    const extra = dict.data.data[0].extra;
    const res = await api.file.api.v1.file.download({ id: extra[type.value] });
    window.common.download(res.data.fileDownloadPreSignedUrl, '', true);
};
</script>
<style scoped></style>
