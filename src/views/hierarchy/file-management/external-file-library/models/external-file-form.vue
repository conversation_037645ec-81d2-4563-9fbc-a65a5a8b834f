<template>
    <alert-content :on-default-save="submit" :showDefaultButtons="!isReadonly">
        <n-form
            ref="formRef"
            :model="data"
            :rules="rules"
            label-placement="left"
            require-mark-placement="left"
            :label-width="110"
            :show-feedback="false"
        >
            <n-grid :cols="24" :y-gap="10" :x-gap="10">
                <n-form-item-gi label="集团文件编号" path="number" :span="12">
                    <n-input v-model:value="data.number" placeholder="无需填写" :disabled="true" />
                </n-form-item-gi>
                <n-form-item-gi label="版本/版次" path="version" :span="12">
                    <n-input v-model:value="data.version" placeholder="无需填写" :disabled="true" />
                </n-form-item-gi>
                <n-form-item-gi label="文件名称" path="name" :span="12">
                    <n-input
                        v-model:value="data.name"
                        placeholder="请输入文件名称"
                        maxlength="50"
                        show-count
                        :disabled="isReadonly"
                    />
                </n-form-item-gi>
                <n-form-item-gi label="文件类别" path="typeDictionaryNodeId" :span="12">
                    <n-tree-select
                        v-model:value="data.typeDictionaryNodeId"
                        :options="bookLibraryOptions"
                        placeholder="请选择文件类别"
                        :multiple="false"
                        filterable
                        clearable
                        show-path
                        :disabled="isReadonly"
                    />
                </n-form-item-gi>
                <n-form-item-gi label="原文件号" path="originalDocNumber" :span="12">
                    <n-input
                        v-model:value="data.originalDocNumber"
                        placeholder="请输入原文件号"
                        :disabled="isReadonly"
                        maxlength="50"
                        show-count
                    />
                </n-form-item-gi>
                <n-form-item-gi label="所属领域" path="domainDictionaryNodeId" :span="12">
                    <n-tree-select
                        v-model:value="data.domainDictionaryNodeId"
                        :options="domainOptions"
                        placeholder="请选择所属领域"
                        :multiple="false"
                        :disabled="isReadonly"
                    />
                </n-form-item-gi>
                <n-form-item-gi label="发文部门" path="publishDepartment" :span="12">
                    <n-input
                        v-model:value="data.publishDepartment"
                        placeholder="请输入发文部门"
                        :disabled="isReadonly"
                        maxlength="50"
                        show-count
                    />
                </n-form-item-gi>
                <n-form-item-gi label="发文号" path="publishDocNumber" :span="12">
                    <n-input
                        v-model:value="data.publishDocNumber"
                        placeholder="请输入发文号"
                        :disabled="isReadonly"
                        maxlength="50"
                        show-count
                    />
                </n-form-item-gi>
                <n-form-item-gi label="发布日期" path="publishDate" :span="12">
                    <n-date-picker
                        v-model:value="data.publishDate"
                        type="date"
                        style="width: 100%"
                        placeholder="请选择发布日期"
                        :disabled="isReadonly"
                    />
                </n-form-item-gi>
                <n-form-item-gi label="实施日期" path="effectiveDate" :span="12">
                    <n-date-picker
                        v-model:value="data.effectiveDate"
                        type="date"
                        style="width: 100%"
                        placeholder="请选择实施日期"
                        :disabled="isReadonly"
                    />
                </n-form-item-gi>
                <n-form-item-gi label="认证方式" :span="24">
                    <n-tree-select
                        v-model:value="data.authenticationDictionaryNodeIds"
                        :options="certTypeOptions"
                        placeholder="请选择认证方式"
                        checkable
                        :multiple="true"
                        :disabled="isReadonly"
                    />
                </n-form-item-gi>
                <n-form-item-gi label="原文件编号" path="originalNumber" :span="12">
                    <n-input
                        v-model:value="data.originalNumber"
                        placeholder="请输入原文件编号"
                        :disabled="isReadonly"
                        maxlength="50"
                        show-count
                    />
                </n-form-item-gi>
                <n-form-item-gi label="原版本版次" path="originalVersion" :span="12">
                    <n-input
                        v-model:value="data.originalVersion"
                        placeholder="请输入原版本版次"
                        :disabled="isReadonly"
                        maxlength="50"
                        show-count
                    />
                </n-form-item-gi>
                <n-form-item-gi label="上传文件" path="fileList" :span="24">
                    <minio-upload
                        class="w-full"
                        v-model:file-list="fileList"
                        :upload-props="{ max: 1, accept: '.pdf', disabled: isReadonly }"
                    >
                        <template #drag>
                            <div class="w-full h-full flex-v justify-center items-center p-10px">
                                <img :src="uploadFileImg" class="w-40px h-40px mt-10px" />
                                <span class="text-14px mt-10px">点击或者拖动文件到该区域来上传</span>
                                <span class="text-12px c-#838383 mt-10px">只支持上传.pdf文件</span>
                            </div>
                        </template>
                    </minio-upload>
                </n-form-item-gi>
            </n-grid>
        </n-form>
    </alert-content>
</template>
<script setup lang="ts">
import { ref } from 'vue';
import minioUpload from '@/components/business/minio-upload.vue';
import uploadFileImg from '@/assets/images/file/upload-file.webp';
import { FormRules } from 'naive-ui';

const props = withDefaults(
    defineProps<{
        row?: any;
        bookLibraryOptions: any[];
        domainOptions: any[];
        certTypeOptions: any[];
        orgType: number;
        readonly?: boolean; // 新增只读属性
    }>(),
    {
        row: null,
        bookLibraryOptions: () => [],
        domainOptions: () => [],
        certTypeOptions: () => [],
        orgType: 3,
        readonly: false
    }
);
const isReadonly = computed(() => props.readonly === true);
const formRef = ref();
const fileList = ref<any[]>([]);
const data = ref({
    number: '', // 自动生成
    version: '', // 自动生成
    name: '',
    typeDictionaryNodeId: null,
    originalDocNumber: null,
    domainDictionaryNodeId: null,
    publishDepartment: null,
    publishDocNumber: null,
    publishDate: null,
    effectiveDate: null,
    authenticationDictionaryNodeIds: [] as string[], // 显式声明类型为 string[]
    originalNumber: '',
    originalVersion: '',
    fileId: null
});
const rules: FormRules = {
    name: { required: true, message: '请输入文件名称', trigger: ['blur', 'input'] },
    typeDictionaryNodeId: { required: true, message: '请选择文件类别', trigger: 'change' },
    originalDocNumber: { required: true, message: '请选择原文件号', trigger: 'change' },
    domainDictionaryNodeId: { required: true, message: '请选择所属领域', trigger: 'change' },
    publishDepartment: { required: true, message: '请选择发文部门', trigger: 'change' },
    publishDocNumber: { required: true, message: '请选择发文号', trigger: 'change' },
    publishDate: { required: true, message: '请选择发布日期', trigger: 'change', type: 'date' },
    effectiveDate: { required: true, message: '请选择实施日期', trigger: 'change', type: 'date' }
};

const submit = async () => {
    await formRef.value
        ?.validate()
        .then(async () => {
            await new Promise((resolve) => {
                window.$dialog.warning({
                    title: `确认${props.row ? '修订' : '新增'}`,
                    content: `确认后将${props.row ? '修订' : '新增'}外部文件，是否确认？`,
                    positiveText: '确认',
                    negativeText: '取消',
                    onPositiveClick: async () => {
                        if (props.row) {
                            await $apis.nebula.api.v1.external.update({
                                ...data.value,
                                orgType: props.orgType,
                                fileId: fileList.value[0]?.response?.id || ''
                            });
                        } else {
                            await $apis.nebula.api.v1.external.create({
                                ...data.value,
                                orgType: props.orgType,
                                fileId: fileList.value[0]?.response?.id || ''
                            });
                        }
                        window.$message.success(`${props.row ? '修订' : '新增'}成功`);
                        resolve(true);
                    }
                });
            });
        })
        .catch((err: any) => {
            window.$message.error(err[0][0].message);
            return Promise.reject();
        });
};
onMounted(() => {
    if (props.row) {
        data.value = props.row;
        if (props.row.fileInfo.fileId) {
            fileList.value = [{ name: props.row.fileInfo.fileName, id: props.row.fileInfo.fileId, status: 'finished' }];
        }
    }
});
</script>
