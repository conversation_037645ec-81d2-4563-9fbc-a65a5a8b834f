<template>
    <div class="user" v-if="oId">
        <n-tabs :bar-width="28" type="line" class="custom-tabs" @update:value="updateChang">
            <n-tab-pane name="part" tab="本级人员"> </n-tab-pane>
            <n-tab-pane name="all" tab="本级及子级人员"></n-tab-pane>
        </n-tabs>
        <n-search-table-page
            ref="searchTablePageRef"
            :data-table-props="{
                columns: optionValues as any,
                size: 'small',
                maxHeight: 'calc(100vh - 640px)',
                scrollX:1200,
                rowKey: (row:any) => row.id,
                'onUpdate:checkedRowKeys': handleCheck
            }"
            :data-api="
                activeTabKey === 'all'
                    ? api.sass.api.v1.organizationUserInfo.allList
                    : api.sass.api.v1.organizationUserInfo.list
            "
            :params="{
                organizationId: oId
            }"
            :search-table-space="{
                size: 20
            }"
            :pagination-props="{
                showQuickJumper: true,
                showSizePicker: true,
                pageSizes: [10, 15, 20, 30, 50, 100]
            }"
            :search-props="{
                addText: '新增',
                showAdd: store.permissions.indexOf('addUser') > -1 && activeTabKey !== 'all',
                searchInputPlaceholder: '请输入用户名/姓名'
            }"
            @add="addUser(null)"
            @reset="init()"
        >
            <template #search_handle_after>
                <n-permission has="bindUser">
                    <n-button type="primary" v-if="activeTabKey !== 'all'" @click="handleBindUser">绑定人员</n-button>
                </n-permission>
                <n-permission has="userSort">
                    <n-button type="primary" v-if="activeTabKey !== 'all'" color="#005eff" @click="handleOperate(oId)"
                        >人员排序</n-button
                    >
                </n-permission>
            </template>
            <template #table_status="{ row }">
                <n-tag size="small" round :bordered="false" :type="row.status ? 'success' : 'error'">{{
                    row.status ? '启用' : '禁用'
                }}</n-tag>
            </template>
            <template #prefix="{ itemCount }"> 共{{ itemCount }}项 </template>
            <template #table_avatar="{ row }">
                <n-image v-if="row.avatar.url" width="20" height="20" object-fit="contain" :src="row.avatar.url" />
                <n-image v-else width="20" height="20" object-fit="contain" :src="fallbackAvatar" />
            </template>
            <template #table_positionIds="{ row }">
                {{ positionIds(row.organizationUserPosition) }}
            </template>
            <template #table_todo="{ row }">
                <n-space justify="center">
                    <n-permission has="editUser">
                        <n-button size="tiny" @click="addUser(row)" type="success" v-if="activeTabKey !== 'all'"
                            >编辑</n-button
                        >
                    </n-permission>
                    <n-permission has="deleteUser">
                        <n-button size="tiny" @click="deleteUser(row)" type="error" v-if="activeTabKey !== 'all'"
                            >解绑</n-button
                        >
                    </n-permission>
                </n-space>
            </template>
        </n-search-table-page>
    </div>
</template>

<script lang="ts" setup>
import { DataTableRowKey, useDialog, useMessage } from 'naive-ui';
import { OrganizationUserListData } from '@/api/sass/api/v1/organization-user-info';
import useStore from '@/store/modules/main';
import { TableColumns } from 'naive-ui/es/data-table/src/interface';
import fallbackAvatar from '@/assets/images/avatar.png';

const dialog = useDialog();
const message = useMessage();
const activeTabKey = ref<string>('part');

const store = useStore();

const props = defineProps<{
    oId: string;
}>();

const selected = ref<string[]>([]);

const emit = defineEmits<{
    (e: 'treeList'): void;
}>();

// 接口
const columns = ref<TableColumns>([
    {
        type: 'selection',
        fixed: 'left'
    },
    {
        title: '序号',
        key: 'key',
        width: 60,
        fixed: 'left',
        align: 'center',
        render: (_: any, index: number) => `${index + 1}`
    },
    {
        title: '用户名',
        key: 'username',
        align: 'center',
        minWidth: 120
    },
    {
        title: '姓名',
        key: 'nickname',
        align: 'center',
        minWidth: 120
    },
    { title: '头像', key: 'avatar', align: 'center', minWidth: 120 },
    {
        title: '用户岗位',
        key: 'positionIds',
        align: 'center',
        minWidth: '120'
    },
    { title: '手机', key: 'mobile', align: 'center', minWidth: 120 },
    { title: '是否有效', key: 'status', align: 'center', minWidth: 80 }
]);

const optionValues = computed(() => {
    return activeTabKey.value == 'all'
        ? columns.value
        : [
              ...columns.value,
              {
                  title: '操作',
                  key: 'todo',
                  align: 'center',
                  minWidth: 120,
                  fixed: 'right'
              }
          ];
});

const positionIds = (organizationUserPosition: Array<{ positionId: string; positionName: string }>) => {
    return organizationUserPosition
        ? organizationUserPosition
              .filter((item) => item.positionName)
              .map((item) => item.positionName)
              .join(',')
        : '';
};

const addUser = (row: OrganizationUserListData | null) => {
    $alert.dialog({
        title: row ? '编辑' : '新增',
        width: '500px',
        content: import('./models/user-form.vue'),
        props: {
            row,
            oId: props.oId,
            onSave: () => init()
        }
    });
};
const handleBindUser = () => {
    $alert.dialog({
        title: '绑定人员',
        width: '500px',
        content: import('./models/bind-users.vue'),
        props: {
            oId: props.oId,
            onSave: () => init()
        }
    });
};
const deleteUser = (row: OrganizationUserListData) => {
    dialog.warning({
        title: '警告',
        content: '确定解绑该条数据么？',
        positiveText: '确定',
        negativeText: '取消',
        onPositiveClick: async () => {
            if (!row.id) return message.error('解绑失败');
            await window.api.sass.api.v1.organizationUserInfo.delete([row.id], props.oId);
            message.success('解绑成功');
            init();
        }
    });
};

const init = () => {
    nextTick(() => searchTablePageRef.value.initData());
    emit('treeList');
};

// 勾选数据
const handleCheck = (row: DataTableRowKey[]) => {
    selected.value = row as string[];
};

const handleOperate = (oId: string) => {
    $alert.dialog({
        title: `用户编辑`,
        width: '60%',
        content: import('./models/sort-list.vue'),
        props: {
            oId,
            onClose: () => {
                nextTick(() => searchTablePageRef.value.initData());
            }
        }
    });
};

const updateChang = async (key: any) => {
    activeTabKey.value = key;
    init();
};
const searchTablePageRef = ref();
defineExpose({ init });
</script>

<style scoped></style>
