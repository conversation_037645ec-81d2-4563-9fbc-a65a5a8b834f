<template>
    <div class="min-h-300px mini-hellp-list">
        <n-search-table-page
            ref="searchTablePageRef"
            :data-table-props="{
                columns: columnData,
                size: 'small',
                maxHeight: '400px'
            }"
            :search-props="{
                showAdd: false,
                searchInputPlaceholder: '请输入文件名称'
            }"
            :params="{
                status,
                moduleName
            }"
            :search-table-space="{
                size: 20
            }"
            :pagination-props="{
                showQuickJumper: true,
                showSizePicker: true,
                pageSizes: [10, 15, 20, 30, 50, 100]
            }"
            :data-api="api.sass.api.v1.dataExport.list"
            @reset="reset"
        >
            <template #search_form_middle>
                <n-space justify="center">
                    <n-input class="w-150px" clearable placeholder="请输入模块名称" v-model:value="moduleName" />
                    <n-select
                        class="w-150px"
                        clearable
                        placeholder="请选择状态"
                        v-model:value="status"
                        :options="statusOptions"
                    />
                </n-space>
            </template>
            <template #table_status="{ row }">
                <n-tag round :bordered="false" :type="getStatusInfo(row.status)?.type || 'default'" size="small">
                    {{ getStatusInfo(row.status)?.label || '未知状态' }}
                </n-tag>
            </template>
            <template #table_createdAt="{ row }">
                <p>{{ dayjs(row.createdAt).format('YYYY-MM-DD HH:mm:ss') }}</p>
            </template>
            <template #table_operation="{ row }">
                <n-button type="primary" size="tiny" :disabled="row.status !== 2" @click="download(row)">
                    下载
                </n-button>
            </template>
        </n-search-table-page>
    </div>
</template>

<script setup lang="ts">
import { ref, computed, reactive } from 'vue';
import dayjs from 'dayjs';
import { TableColumns } from 'naive-ui/es/data-table/src/interface';

const moduleName = ref();
type StatusOption = {
    label: string;
    value: number;
    type: 'info' | 'success' | 'warning' | 'error';
};

const statusOptions: StatusOption[] = [
    { label: '导出中', value: 1, type: 'info' },
    { label: '可下载', value: 2, type: 'success' },
    { label: '已下载', value: 3, type: 'warning' },
    { label: '导出失败', value: 4, type: 'error' }
];

const statusMap = computed(() => {
    const map = new Map<number, (typeof statusOptions)[number]>();
    statusOptions.forEach((option) => map.set(option.value, option));
    return map;
});

const getStatusInfo = (statusValue: number | undefined) => {
    return statusValue !== undefined ? statusMap.value.get(statusValue) : undefined;
};

const status = ref<number | undefined | null>();
const columnData: TableColumns = [
    {
        title: '序号',
        key: 'serialNumber',
        width: 60,
        align: 'center',
        render: (row: any, index: number) => index + 1
    },
    {
        title: '导出模块',
        key: 'moduleName',
        align: 'center',
        ellipsis: { tooltip: true }
    },
    {
        title: '文件名称',
        key: 'fileName',
        align: 'center',
        ellipsis: { tooltip: true }
    },
    {
        title: '发起时间',
        key: 'createdAt',
        align: 'center',
        ellipsis: { tooltip: true }
    },
    {
        title: '状态',
        key: 'status',
        align: 'center',
        ellipsis: { tooltip: true },
        width: 100
    },
    {
        title: '操作',
        key: 'operation',
        align: 'center',
        width: 80
    }
];

const downloadingState = reactive<Record<string, boolean>>({});
const downloadedMap = ref<Record<string, boolean>>({});

const getRowKey = (row: any) => `${row.fileId}_${row.createdAt}`;

const download = async (row: any) => {
    const key = getRowKey(row);
    if (downloadingState[key] || downloadedMap.value[key]) return;
    downloadingState[key] = true;
    try {
        downloadedMap.value[key] = true;
        const res = await api.file.api.v1.file.download({ id: row.fileId });
        await downloadFile(res.data.fileDownloadPreSignedUrl);
        await api.sass.api.v1.dataExport.downloadStatus(row.id);
        await searchTablePageRef.value?.initData();
    } finally {
        downloadingState[key] = false;
    }
};
const downloadFile = async (url: string) => {
    const exists = await checkFileExists(url);
    if (!exists) {
        window.$message.error('文件损坏或不存在');
        return;
    }
    const link = document.createElement('a');
    link.href = url;
    link.style.display = 'none';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
};
// 检查是否能正常访问url地址
const checkFileExists = async (url: string) => {
    try {
        const response = await fetch(url);
        return response.ok;
    } catch (error) {
        window.$message.error('文件检查失败');
        console.error('文件检查错误===>[' + error + ']');
        return false;
    }
};

const reset = () => {
    status.value = null;
    moduleName.value = null;
    nextTick(() => searchTablePageRef.value?.initData());
};

const searchTablePageRef = ref();
</script>

<style scoped lang="less"></style>
