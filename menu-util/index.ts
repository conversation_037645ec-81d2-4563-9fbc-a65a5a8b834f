import { readFileSync, writeFileSync } from 'fs';
import { resolve } from 'path';
import { glob } from 'glob';

interface MenuItem {
    path: string;
    name: string;
    meta?: {
        title?: string;
        icon?: string;
        isFullPage?: boolean;
        permissions?: Array<{ name: string; code: string }>;
        [key: string]: any;
    };
    redirect?: string;
    children?: MenuItem[];
}

function extractRouteObject(content: string): any[] {
    try {
        // 移除导入语句和类型声明
        const cleanContent = content
            .replace(/import\s+.*?from\s+['"][^'"]*['"];?\s*/g, '')
            .replace(/import\s+\{[^}]*\}\s+from\s+['"][^'"]*['"];?\s*/g, '');

        // 查找 routes 数组的定义
        const routeMatch = cleanContent.match(/const\s+routes\s*:\s*RouteRecordRaw\[\]\s*=\s*(\[[\s\S]*?\]);/);
        if (!routeMatch) {
            return [];
        }

        let routeStr = routeMatch[1];

        // 移除注释
        routeStr = routeStr
            .replace(/\/\/.*$/gm, '') // 单行注释
            .replace(/\/\*[\s\S]*?\*\//g, ''); // 多行注释

        // 处理动态导入，将其替换为字符串
        routeStr = routeStr
            .replace(/component:\s*\(\)\s*=>\s*import\(['"]([^'"]+)['"]\)/g, (_, path) => {
                return `component: "${path}"`;
            })
            .replace(/component:\s*RouterView/g, 'component: "RouterView"');

        // 移除尾随逗号
        routeStr = routeStr.replace(/,(\s*[}\]])/g, '$1');

        // 尝试使用 eval 来解析 JavaScript 对象
        // 注意：这里使用 eval 是安全的，因为我们只处理已知的路由配置文件
        try {
            const routes = eval(`(${routeStr})`);
            return Array.isArray(routes) ? routes : [];
        } catch (evalError) {
            console.warn('Failed to eval route string:', evalError);
            return [];
        }

    } catch (error) {
        console.warn('Error extracting route object:', error);
        return [];
    }
}

function flattenMenus(routes: any[]): MenuItem[] {
    const menus: MenuItem[] = [];

    for (const route of routes) {
        if (!route || !route.path) continue;

        const menu: MenuItem = {
            path: route.path,
            name: route.name || route.path,
        };

        if (route.meta) {
            menu.meta = { ...route.meta };
            // 移除 component 引用，只保留菜单相关信息
            if (menu.meta.component) {
                delete menu.meta.component;
            }
        }

        if (route.redirect) {
            menu.redirect = route.redirect;
        }

        if (route.children && Array.isArray(route.children)) {
            menu.children = flattenMenus(route.children);
        }

        menus.push(menu);
    }

    return menus;
}

function extractNumberFromFilename(filename: string): number {
    // 从文件名中提取数字前缀，例如 "0-entry.ts" -> 0, "100-system.ts" -> 100
    const match = filename.match(/(\d+)-/);
    return match ? parseInt(match[1], 10) : 999; // 没有数字前缀的文件排在最后
}

async function extractMenusFromRoutes() {
    try {
        // 获取所有路由模块文件
        const routeFiles = glob.sync('../src/router/modules/*.ts', {
            cwd: __dirname,
            absolute: true
        });

        // 按文件名中的数字前缀排序
        routeFiles.sort((a, b) => {
            const numA = extractNumberFromFilename(a);
            const numB = extractNumberFromFilename(b);
            return numA - numB;
        });

        console.log('Processing files in order:');
        routeFiles.forEach((file, index) => {
            const filename = file.split('/').pop() || '';
            const num = extractNumberFromFilename(filename);
            console.log(`  ${index + 1}. ${filename} (order: ${num})`);
        });
        console.log('');

        const allMenus: MenuItem[] = [];

        for (const file of routeFiles) {
            try {
                const filename = file.split('/').pop() || '';
                console.log(`Processing file: ${filename}`);
                const content = readFileSync(file, 'utf-8');
                const routes = extractRouteObject(content);

                if (routes.length > 0) {
                    const menus = flattenMenus(routes);
                    allMenus.push(...menus);
                    console.log(`  Found ${menus.length} menu items`);
                } else {
                    console.log(`  No valid routes found`);
                }
            } catch (fileError) {
                console.warn(`Failed to process file ${file}:`, fileError);
            }
        }

        // 写入菜单文件
        writeFileSync(
            resolve(__dirname, 'menus.json'),
            JSON.stringify(allMenus, null, 4)
        );

        console.log('\nMenus extracted successfully!');
        console.log(`Total menu items found: ${allMenus.length}`);
        console.log('Menu order based on filename prefixes:');
        allMenus.forEach((menu, index) => {
            console.log(`  ${index + 1}. ${menu.meta?.title || menu.name} (${menu.path})`);
        });

    } catch (error) {
        console.error('Error extracting menus:', error);
        process.exit(1);
    }
}

extractMenusFromRoutes();
